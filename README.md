# مولد المشاريع الذكي - AI Project Builder

🚀 تطبيق ويب متكامل يعمل كمولد مشاريع ذكي باستخدام الذكاء الصناعي

## 📋 الوصف

مولد المشاريع الذكي هو تطبيق ويب تفاعلي يمكّن المطورين من إنشاء مشاريع برمجية كاملة باستخدام الذكاء الصناعي. ما عليك سوى وصف فكرة مشروعك، وسيقوم الذكاء الصناعي بإنشاء الكود والملفات اللازمة تلقائياً.

## ✨ الميزات الرئيسية

### 💬 دردشة ذكية
- واجهة دردشة تفاعلية مع الذكاء الصناعي
- تحليل ذكي لمتطلبات المشروع
- إنشاء مشاريع متنوعة (React, HTML, Node.js, إلخ)

### 🌳 إدارة الملفات
- عرض هيكل المشروع في شجرة تفاعلية
- إضافة وحذف وإعادة تسمية الملفات والمجلدات
- تنظيم تلقائي للملفات حسب نوع المشروع

### 💻 محرر كود متقدم
- محرر Monaco Editor مع تلوين الكود
- دعم الإكمال التلقائي
- إعدادات قابلة للتخصيص (حجم الخط، المظهر)
- اختصارات لوحة المفاتيح

### 👁️ معاينة مباشرة
- معاينة فورية للمشاريع
- دعم أوضاع العرض المختلفة (سطح المكتب، جهاز لوحي، هاتف)
- تحديث تلقائي عند تغيير الكود

### 📤 تصدير متقدم
- تصدير المشروع كملف ZIP
- رفع مباشر إلى GitHub
- إنشاء README.md تلقائياً
- إضافة ملفات التكوين (.gitignore, package.json)

## 🛠️ التقنيات المستخدمة

### الواجهة الأمامية
- **React.js** - مكتبة واجهة المستخدم
- **Tailwind CSS** - إطار عمل CSS
- **Monaco Editor** - محرر الكود
- **Axios** - للتواصل مع API
- **React Icons** - الأيقونات

### الخادم الخلفي
- **Node.js** - بيئة تشغيل JavaScript
- **Express.js** - إطار عمل الخادم
- **OpenAI API** - الذكاء الصناعي
- **Archiver** - إنشاء ملفات ZIP
- **GitHub API** - التكامل مع GitHub

### أدوات التطوير
- **Vite** - أداة البناء
- **ESLint** - فحص الكود
- **PostCSS** - معالج CSS

## 🚀 التثبيت والتشغيل

### المتطلبات
- Node.js (الإصدار 14 أو أحدث)
- npm أو yarn
- مفتاح OpenAI API (اختياري)

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/ai-project-builder.git
cd ai-project-builder
```

2. **تثبيت المتطلبات**
```bash
npm install
```

3. **إعداد متغيرات البيئة**
```bash
# إنشاء ملف .env في المجلد الجذر
echo "OPENAI_API_KEY=your_openai_api_key_here" > .env
```

4. **تشغيل الخادم الخلفي**
```bash
npm run server
```

5. **تشغيل الواجهة الأمامية**
```bash
npm run dev
```

6. **فتح التطبيق**
افتح المتصفح وانتقل إلى `http://localhost:3000`

## 📁 هيكل المشروع

```
ai-project-builder/
├── public/                 # الملفات العامة
├── src/                   # كود الواجهة الأمامية
│   ├── components/        # مكونات React
│   │   ├── ChatPanel.jsx  # لوحة الدردشة
│   │   ├── FileTree.jsx   # شجرة الملفات
│   │   ├── CodeEditor.jsx # محرر الكود
│   │   ├── Preview.jsx    # المعاينة
│   │   └── Header.jsx     # الرأس
│   ├── context/          # إدارة الحالة
│   ├── services/         # خدمات API
│   └── App.jsx           # المكون الرئيسي
├── server/               # كود الخادم الخلفي
│   ├── services/         # خدمات الخادم
│   └── index.js          # نقطة دخول الخادم
├── package.json          # تكوين المشروع
└── README.md            # هذا الملف
```

## 🎯 كيفية الاستخدام

1. **ابدأ محادثة جديدة**
   - اكتب وصفاً لمشروعك في صندوق الدردشة
   - مثال: "أنشئ لي تطبيق لإدارة المهام باستخدام React"

2. **استعرض النتائج**
   - سيقوم الذكاء الصناعي بإنشاء هيكل المشروع
   - استعرض الملفات في الشجرة الجانبية

3. **حرر الكود**
   - انقر على أي ملف لفتحه في المحرر
   - قم بالتعديل حسب احتياجاتك

4. **شاهد المعاينة**
   - فعّل المعاينة لرؤية النتيجة مباشرة
   - جرب أوضاع العرض المختلفة

5. **صدّر المشروع**
   - احفظ المشروع كملف ZIP
   - أو ارفعه مباشرة إلى GitHub

## 🔧 التخصيص

### إضافة نماذج مشاريع جديدة
يمكنك إضافة نماذج مشاريع جديدة في `server/services/aiService.js`:

```javascript
const projectTemplates = {
  yourTemplate: {
    name: 'اسم النموذج',
    description: 'وصف النموذج',
    files: [
      // قائمة الملفات
    ]
  }
}
```

### تخصيص واجهة المستخدم
- عدّل ألوان Tailwind في `tailwind.config.js`
- أضف أنماط مخصصة في `src/index.css`

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push إلى الفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## 📝 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🙏 شكر وتقدير

- [OpenAI](https://openai.com/) للذكاء الصناعي
- [Monaco Editor](https://microsoft.github.io/monaco-editor/) لمحرر الكود
- [React](https://reactjs.org/) لمكتبة واجهة المستخدم
- [Tailwind CSS](https://tailwindcss.com/) لإطار عمل CSS

## 📞 التواصل

إذا كان لديك أي أسئلة أو اقتراحات، لا تتردد في التواصل معنا:

- إنشاء Issue في GitHub
- إرسال Pull Request
- التواصل عبر البريد الإلكتروني

---

⭐ إذا أعجبك هذا المشروع، لا تنس إعطاؤه نجمة على GitHub!
