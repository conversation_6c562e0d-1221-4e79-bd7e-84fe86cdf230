import React, { useState } from 'react'
import { 
  FiX, 
  FiDownload, 
  FiGithub, 
  FiLoader, 
  FiCheck, 
  FiAlertCircle,
  FiExternalLink
} from 'react-icons/fi'
import { exportAsZip, exportToGitHub } from '../services/exportService'

function ExportModal({ isOpen, onClose, project, files }) {
  const [activeTab, setActiveTab] = useState('zip')
  const [isExporting, setIsExporting] = useState(false)
  const [exportResult, setExportResult] = useState(null)
  const [githubToken, setGithubToken] = useState('')
  const [repoName, setRepoName] = useState('')

  const handleZipExport = async () => {
    setIsExporting(true)
    setExportResult(null)

    try {
      const result = await exportAsZip(project, files)
      setExportResult(result)
    } catch (error) {
      setExportResult({
        success: false,
        error: 'حدث خطأ أثناء التصدير'
      })
    } finally {
      setIsExporting(false)
    }
  }

  const handleGitHubExport = async () => {
    if (!githubToken.trim() || !repoName.trim()) {
      setExportResult({
        success: false,
        error: 'يرجى ملء جميع الحقول المطلوبة'
      })
      return
    }

    setIsExporting(true)
    setExportResult(null)

    try {
      const result = await exportToGitHub(project, files, githubToken.trim(), repoName.trim())
      setExportResult(result)
    } catch (error) {
      setExportResult({
        success: false,
        error: 'حدث خطأ أثناء الرفع إلى GitHub'
      })
    } finally {
      setIsExporting(false)
    }
  }

  const resetModal = () => {
    setActiveTab('zip')
    setIsExporting(false)
    setExportResult(null)
    setGithubToken('')
    setRepoName('')
  }

  const handleClose = () => {
    resetModal()
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-800">تصدير المشروع</h2>
          <button
            onClick={handleClose}
            className="p-1 hover:bg-gray-100 rounded-full transition-colors"
          >
            <FiX className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200">
          <button
            onClick={() => setActiveTab('zip')}
            className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
              activeTab === 'zip'
                ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
            }`}
          >
            <FiDownload className="w-4 h-4 inline ml-2" />
            ملف ZIP
          </button>
          <button
            onClick={() => setActiveTab('github')}
            className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
              activeTab === 'github'
                ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
            }`}
          >
            <FiGithub className="w-4 h-4 inline ml-2" />
            GitHub
          </button>
        </div>

        {/* Content */}
        <div className="p-4">
          {activeTab === 'zip' && (
            <div className="space-y-4">
              <div className="text-sm text-gray-600">
                <p className="mb-2">سيتم تصدير المشروع كملف ZIP يحتوي على:</p>
                <ul className="list-disc list-inside space-y-1 text-xs">
                  <li>جميع ملفات المشروع</li>
                  <li>ملف README.md</li>
                  <li>ملف .gitignore</li>
                  <li>ملف package.json (إن أمكن)</li>
                </ul>
              </div>

              <button
                onClick={handleZipExport}
                disabled={isExporting}
                className="w-full bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
              >
                {isExporting ? (
                  <>
                    <FiLoader className="w-4 h-4 animate-spin" />
                    جاري التصدير...
                  </>
                ) : (
                  <>
                    <FiDownload className="w-4 h-4" />
                    تحميل ZIP
                  </>
                )}
              </button>
            </div>
          )}

          {activeTab === 'github' && (
            <div className="space-y-4">
              <div className="text-sm text-gray-600">
                <p className="mb-2">رفع المشروع إلى GitHub:</p>
                <ul className="list-disc list-inside space-y-1 text-xs">
                  <li>سيتم إنشاء مستودع جديد</li>
                  <li>رفع جميع الملفات</li>
                  <li>إضافة README.md تلقائياً</li>
                </ul>
              </div>

              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    رمز GitHub الشخصي
                  </label>
                  <input
                    type="password"
                    value={githubToken}
                    onChange={(e) => setGithubToken(e.target.value)}
                    placeholder="ghp_xxxxxxxxxxxxxxxxxxxx"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    <a 
                      href="https://github.com/settings/tokens" 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-blue-500 hover:underline inline-flex items-center gap-1"
                    >
                      إنشاء رمز جديد
                      <FiExternalLink className="w-3 h-3" />
                    </a>
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    اسم المستودع
                  </label>
                  <input
                    type="text"
                    value={repoName}
                    onChange={(e) => setRepoName(e.target.value)}
                    placeholder="my-awesome-project"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                  />
                </div>
              </div>

              <button
                onClick={handleGitHubExport}
                disabled={isExporting || !githubToken.trim() || !repoName.trim()}
                className="w-full bg-gray-800 text-white py-2 px-4 rounded-lg hover:bg-gray-900 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
              >
                {isExporting ? (
                  <>
                    <FiLoader className="w-4 h-4 animate-spin" />
                    جاري الرفع...
                  </>
                ) : (
                  <>
                    <FiGithub className="w-4 h-4" />
                    رفع إلى GitHub
                  </>
                )}
              </button>
            </div>
          )}

          {/* Result Message */}
          {exportResult && (
            <div className={`mt-4 p-3 rounded-lg flex items-start gap-2 ${
              exportResult.success 
                ? 'bg-green-50 text-green-700 border border-green-200' 
                : 'bg-red-50 text-red-700 border border-red-200'
            }`}>
              {exportResult.success ? (
                <FiCheck className="w-4 h-4 mt-0.5 flex-shrink-0" />
              ) : (
                <FiAlertCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
              )}
              <div className="flex-1">
                <p className="text-sm font-medium">
                  {exportResult.success ? 'نجح التصدير!' : 'فشل التصدير'}
                </p>
                <p className="text-xs mt-1">
                  {exportResult.message || exportResult.error}
                </p>
                {exportResult.repoUrl && (
                  <a
                    href={exportResult.repoUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-xs text-blue-600 hover:underline inline-flex items-center gap-1 mt-2"
                  >
                    عرض المستودع
                    <FiExternalLink className="w-3 h-3" />
                  </a>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-2 p-4 border-t border-gray-200">
          <button
            onClick={handleClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            إغلاق
          </button>
        </div>
      </div>
    </div>
  )
}

export default ExportModal
