import axios from 'axios'
import { saveAs } from 'file-saver'

const API_BASE_URL = '/api'

export async function exportAsZip(project, files) {
  try {
    const response = await axios.post(`${API_BASE_URL}/export/zip`, {
      project,
      files
    }, {
      responseType: 'blob'
    })

    // Create blob and download
    const blob = new Blob([response.data], { type: 'application/zip' })
    const fileName = `${project.name || 'project'}.zip`
    saveAs(blob, fileName)

    return {
      success: true,
      message: 'تم تصدير المشروع بنجاح'
    }
  } catch (error) {
    console.error('Export ZIP error:', error)
    return {
      success: false,
      error: error.response?.data?.error || 'حدث خطأ أثناء تصدير المشروع'
    }
  }
}

export async function exportToGitHub(project, files, githubToken, repoName) {
  try {
    if (!githubToken || !repoName) {
      return {
        success: false,
        error: 'يرجى تقديم رمز GitHub واسم المستودع'
      }
    }

    const response = await axios.post(`${API_BASE_URL}/export/github`, {
      project,
      files,
      githubToken,
      repoName
    })

    return {
      success: true,
      repoUrl: response.data.repoUrl,
      message: response.data.message
    }
  } catch (error) {
    console.error('Export GitHub error:', error)
    return {
      success: false,
      error: error.response?.data?.error || 'حدث خطأ أثناء الرفع إلى GitHub'
    }
  }
}

// Client-side ZIP creation using JSZip (alternative method)
export async function createClientZip(project, files) {
  try {
    const JSZip = (await import('jszip')).default
    const zip = new JSZip()

    // Add project metadata
    const projectInfo = {
      name: project.name,
      description: project.description,
      type: project.type,
      createdAt: project.createdAt,
      generatedBy: 'AI Project Builder',
      version: '1.0.0'
    }
    zip.file('project.json', JSON.stringify(projectInfo, null, 2))

    // Add README
    const readmeContent = generateClientReadme(project, files)
    zip.file('README.md', readmeContent)

    // Add all project files
    Object.values(files).forEach(file => {
      if (file.content) {
        zip.file(file.path, file.content)
      }
    })

    // Add .gitignore
    const gitignoreContent = generateClientGitignore(project.type)
    zip.file('.gitignore', gitignoreContent)

    // Generate ZIP
    const blob = await zip.generateAsync({ type: 'blob' })
    const fileName = `${project.name || 'project'}.zip`
    saveAs(blob, fileName)

    return {
      success: true,
      message: 'تم تصدير المشروع بنجاح'
    }
  } catch (error) {
    console.error('Client ZIP creation error:', error)
    return {
      success: false,
      error: 'حدث خطأ أثناء إنشاء ملف ZIP'
    }
  }
}

function generateClientReadme(project, files) {
  const filesList = Object.values(files)
    .map(file => `- \`${file.path}\` - ${getFileDescription(file)}`)
    .join('\n')

  return `# ${project.name}

${project.description}

## وصف المشروع

هذا المشروع تم إنشاؤه بواسطة **مولد المشاريع الذكي** باستخدام الذكاء الصناعي.

**نوع المشروع:** ${getProjectTypeArabic(project.type)}
**تاريخ الإنشاء:** ${new Date(project.createdAt).toLocaleDateString('ar-SA')}

## هيكل الملفات

${filesList}

## كيفية التشغيل

${getRunInstructions(project.type)}

## المتطلبات

${getRequirements(project.type)}

## الميزات

${project.features ? project.features.map(feature => `- ${feature}`).join('\n') : '- تم إنشاؤه بواسطة الذكاء الصناعي'}

## التقنيات المستخدمة

${project.technologies ? project.technologies.map(tech => `- ${tech}`).join('\n') : getDefaultTechnologies(project.type)}

---

تم إنشاء هذا المشروع بواسطة [مولد المشاريع الذكي](https://github.com/your-username/ai-project-builder)
`
}

function getFileDescription(file) {
  const descriptions = {
    'html': 'ملف HTML رئيسي',
    'css': 'ملف أنماط CSS',
    'javascript': 'ملف JavaScript',
    'json': 'ملف تكوين JSON',
    'markdown': 'ملف توثيق Markdown',
    'text': 'ملف نصي'
  }
  return descriptions[file.language] || 'ملف المشروع'
}

function getProjectTypeArabic(type) {
  const types = {
    'react': 'تطبيق React',
    'html': 'موقع HTML',
    'nodejs': 'API Node.js',
    'vue': 'تطبيق Vue.js',
    'angular': 'تطبيق Angular'
  }
  return types[type] || 'مشروع ويب'
}

function getRunInstructions(type) {
  const instructions = {
    'react': `\`\`\`bash
# تثبيت المتطلبات
npm install

# تشغيل المشروع في وضع التطوير
npm start

# بناء المشروع للإنتاج
npm run build
\`\`\``,
    'nodejs': `\`\`\`bash
# تثبيت المتطلبات
npm install

# تشغيل الخادم
npm start

# تشغيل في وضع التطوير
npm run dev
\`\`\``,
    'html': `\`\`\`bash
# افتح ملف index.html في المتصفح
# أو استخدم خادم محلي مثل:
npx serve .
# أو
python -m http.server 8000
\`\`\``
  }
  return instructions[type] || 'افتح ملف index.html في المتصفح'
}

function getRequirements(type) {
  const requirements = {
    'react': '- Node.js (الإصدار 14 أو أحدث)\n- npm أو yarn',
    'nodejs': '- Node.js (الإصدار 14 أو أحدث)\n- npm أو yarn',
    'html': '- متصفح ويب حديث\n- خادم ويب محلي (اختياري)'
  }
  return requirements[type] || 'متصفح ويب حديث'
}

function getDefaultTechnologies(type) {
  const technologies = {
    'react': '- React.js\n- JavaScript (ES6+)\n- HTML5\n- CSS3',
    'nodejs': '- Node.js\n- Express.js\n- JavaScript (ES6+)',
    'html': '- HTML5\n- CSS3\n- JavaScript'
  }
  return technologies[type] || '- HTML5\n- CSS3\n- JavaScript'
}

function generateClientGitignore(type) {
  const common = `# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs
*.log
`

  const specific = {
    'react': `
# React build
build/
dist/

# Testing
coverage/
`,
    'nodejs': `
# Node.js
dist/
build/
*.tgz
*.tar.gz
`,
    'html': `
# Build files
dist/
build/
`
  }

  return common + (specific[type] || '')
}
