# 🚀 دليل البدء السريع - مولد المشاريع الذكي

## التشغيل السريع (خطوة واحدة)

```bash
npm start
```

هذا الأمر سيقوم بـ:
- تثبيت جميع المتطلبات تلقائياً
- إعداد ملف البيئة (.env)
- تشغيل الخادم الخلفي والواجهة الأمامية

## التشغيل اليدوي

### 1. تثبيت المتطلبات
```bash
npm install
```

### 2. إعداد متغيرات البيئة
```bash
cp .env.example .env
```

ثم عدّل ملف `.env` وأضف مفتاح OpenAI API:
```
OPENAI_API_KEY=your_actual_api_key_here
```

### 3. تشغيل الخادم الخلفي
```bash
npm run server
```

### 4. تشغيل الواجهة الأمامية (في terminal آخر)
```bash
npm run dev
```

## 🌐 الوصول للتطبيق

افتح المتصفح وانتقل إلى: `http://localhost:3000`

## 🔑 الحصول على مفتاح OpenAI API

1. انتقل إلى [OpenAI API Keys](https://platform.openai.com/api-keys)
2. سجل الدخول أو أنشئ حساب جديد
3. انقر على "Create new secret key"
4. انسخ المفتاح وضعه في ملف `.env`

## 📝 أمثلة للتجربة

جرب هذه الأمثلة في الدردشة:

- "أنشئ لي تطبيق لإدارة المهام باستخدام React"
- "أريد موقع شخصي بسيط بـHTML وCSS"
- "اعمل لي API بسيط بـNode.js وExpress"
- "تطبيق حاسبة بـJavaScript"

## 🛠️ أوامر مفيدة

```bash
# تشغيل الخادم الخلفي فقط
npm run server

# تشغيل الواجهة الأمامية فقط
npm run dev

# تشغيل الخادم الخلفي مع إعادة التحميل التلقائي
npm run dev:server

# بناء المشروع للإنتاج
npm run build

# معاينة البناء
npm run preview

# فحص الكود
npm run lint
```

## ❓ حل المشاكل الشائعة

### المنفذ مستخدم بالفعل
إذا كان المنفذ 3000 أو 5000 مستخدماً:
```bash
# قتل العمليات على المنفذ 3000
npx kill-port 3000

# قتل العمليات على المنفذ 5000
npx kill-port 5000
```

### خطأ في تثبيت المتطلبات
```bash
# مسح cache npm
npm cache clean --force

# حذف node_modules وإعادة التثبيت
rm -rf node_modules package-lock.json
npm install
```

### مشاكل في الذكاء الصناعي
- تأكد من صحة مفتاح OpenAI API
- تحقق من وجود رصيد في حساب OpenAI
- التطبيق يعمل بدون مفتاح API لكن بوظائف محدودة

## 📞 المساعدة

إذا واجهت أي مشاكل:
1. راجع ملف [README.md](README.md) للتفاصيل الكاملة
2. تحقق من console المتصفح للأخطاء
3. تحقق من terminal للرسائل
4. أنشئ Issue في GitHub

---

🎉 **مبروك! أنت الآن جاهز لإنشاء مشاريع رائعة بالذكاء الصناعي!**
