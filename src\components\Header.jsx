import React, { useState } from 'react'
import {
  FiMenu,
  <PERSON>Eye,
  FiEyeOff,
  FiDownload,
  FiGithub,
  FiRefreshCw,
  FiSettings
} from 'react-icons/fi'
import { useProject } from '../context/ProjectContext'
import ExportModal from './ExportModal'

function Header({
  onTogglePreview,
  onToggleSidebar,
  showPreview,
  sidebarCollapsed
}) {
  const { project, files, clearProject, isGenerating } = useProject()
  const [showExportModal, setShowExportModal] = useState(false)

  const handleExport = () => {
    setShowExportModal(true)
  }

  const handleNewProject = () => {
    if (window.confirm('هل أنت متأكد من إنشاء مشروع جديد؟ سيتم فقدان المشروع الحالي.')) {
      clearProject()
    }
  }

  return (
    <header className="bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
      {/* Right side - Logo and controls */}
      <div className="flex items-center gap-4">
        <button
          onClick={onToggleSidebar}
          className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          title={sidebarCollapsed ? 'إظهار الشريط الجانبي' : 'إخفاء الشريط الجانبي'}
        >
          <FiMenu className="w-5 h-5" />
        </button>

        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">AI</span>
          </div>
          <h1 className="text-xl font-bold text-gray-800">مولد المشاريع الذكي</h1>
        </div>

        {project && (
          <div className="text-sm text-gray-600 bg-gray-100 px-3 py-1 rounded-full">
            {project.name || 'مشروع جديد'}
          </div>
        )}
      </div>

      {/* Left side - Action buttons */}
      <div className="flex items-center gap-2">
        {/* Preview toggle */}
        <button
          onClick={onTogglePreview}
          className={`p-2 rounded-lg transition-colors ${
            showPreview 
              ? 'bg-blue-100 text-blue-600 hover:bg-blue-200' 
              : 'hover:bg-gray-100 text-gray-600'
          }`}
          title={showPreview ? 'إخفاء المعاينة' : 'إظهار المعاينة'}
        >
          {showPreview ? <FiEyeOff className="w-5 h-5" /> : <FiEye className="w-5 h-5" />}
        </button>

        {/* Export button */}
        {project && (
          <button
            onClick={handleExport}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors text-gray-600"
            title="تصدير المشروع"
            disabled={isGenerating}
          >
            <FiDownload className="w-5 h-5" />
          </button>
        )}

        {/* New project */}
        <button
          onClick={handleNewProject}
          className="p-2 hover:bg-gray-100 rounded-lg transition-colors text-gray-600"
          title="مشروع جديد"
          disabled={isGenerating}
        >
          <FiRefreshCw className={`w-5 h-5 ${isGenerating ? 'animate-spin' : ''}`} />
        </button>

        {/* Settings */}
        <button
          className="p-2 hover:bg-gray-100 rounded-lg transition-colors text-gray-600"
          title="الإعدادات"
        >
          <FiSettings className="w-5 h-5" />
        </button>
      </div>

      {/* Export Modal */}
      <ExportModal
        isOpen={showExportModal}
        onClose={() => setShowExportModal(false)}
        project={project}
        files={files}
      />
    </header>
  )
}

export default Header
