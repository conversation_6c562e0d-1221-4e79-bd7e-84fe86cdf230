{"name": "ai-project-builder", "private": true, "version": "1.0.0", "type": "module", "description": "AI-powered project generator with intelligent code creation", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "server": "node server/index.js", "start": "node start.js", "dev:server": "nodemon server/index.js", "dev:client": "vite"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@monaco-editor/react": "^4.6.0", "axios": "^1.6.0", "express": "^4.18.2", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "archiver": "^6.0.1", "openai": "^4.20.1", "react-icons": "^4.12.0", "react-sortable-tree": "^0.3.1", "uuid": "^9.0.1", "jszip": "^3.10.1", "file-saver": "^2.0.5", "prismjs": "^1.29.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.0", "vite": "^4.5.0", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "tailwindcss": "^3.3.5", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "nodemon": "^3.0.1"}}