import archiver from 'archiver'
import { Readable } from 'stream'

export async function createZipArchive(project, files) {
  return new Promise((resolve, reject) => {
    try {
      // Create a new archiver instance
      const archive = archiver('zip', {
        zlib: { level: 9 } // Maximum compression
      })

      const chunks = []

      // Collect data chunks
      archive.on('data', (chunk) => {
        chunks.push(chunk)
      })

      // Handle archive completion
      archive.on('end', () => {
        const buffer = Buffer.concat(chunks)
        resolve(buffer)
      })

      // Handle errors
      archive.on('error', (err) => {
        reject(err)
      })

      // Add project metadata
      const projectInfo = {
        name: project.name,
        description: project.description,
        type: project.type,
        createdAt: project.createdAt,
        generatedBy: 'AI Project Builder',
        version: '1.0.0'
      }

      archive.append(JSON.stringify(projectInfo, null, 2), { 
        name: 'project.json' 
      })

      // Add README file
      const readmeContent = generateReadme(project, files)
      archive.append(readmeContent, { name: 'README.md' })

      // Add all project files
      Object.values(files).forEach(file => {
        if (file.content) {
          archive.append(file.content, { name: file.path })
        }
      })

      // Add package.json if it's a Node.js project and doesn't exist
      if (project.type === 'nodejs' || project.type === 'react') {
        const hasPackageJson = Object.values(files).some(file => 
          file.name === 'package.json'
        )

        if (!hasPackageJson) {
          const packageJson = generatePackageJson(project)
          archive.append(JSON.stringify(packageJson, null, 2), { 
            name: 'package.json' 
          })
        }
      }

      // Add .gitignore file
      const gitignoreContent = generateGitignore(project.type)
      archive.append(gitignoreContent, { name: '.gitignore' })

      // Finalize the archive
      archive.finalize()

    } catch (error) {
      reject(error)
    }
  })
}

function generateReadme(project, files) {
  const filesList = Object.values(files)
    .map(file => `- \`${file.path}\` - ${getFileDescription(file)}`)
    .join('\n')

  return `# ${project.name}

${project.description}

## وصف المشروع

هذا المشروع تم إنشاؤه بواسطة **مولد المشاريع الذكي** باستخدام الذكاء الصناعي.

**نوع المشروع:** ${getProjectTypeArabic(project.type)}
**تاريخ الإنشاء:** ${new Date(project.createdAt).toLocaleDateString('ar-SA')}

## هيكل الملفات

${filesList}

## كيفية التشغيل

${getRunInstructions(project.type)}

## المتطلبات

${getRequirements(project.type)}

## الميزات

${project.features ? project.features.map(feature => `- ${feature}`).join('\n') : '- تم إنشاؤه بواسطة الذكاء الصناعي'}

## التقنيات المستخدمة

${project.technologies ? project.technologies.map(tech => `- ${tech}`).join('\n') : getDefaultTechnologies(project.type)}

## المساهمة

يمكنك المساهمة في تطوير هذا المشروع عن طريق:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة الجديدة
3. إضافة التحسينات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

---

تم إنشاء هذا المشروع بواسطة [مولد المشاريع الذكي](https://github.com/your-username/ai-project-builder)
`
}

function getFileDescription(file) {
  const descriptions = {
    'html': 'ملف HTML رئيسي',
    'css': 'ملف أنماط CSS',
    'javascript': 'ملف JavaScript',
    'json': 'ملف تكوين JSON',
    'markdown': 'ملف توثيق Markdown',
    'text': 'ملف نصي'
  }
  return descriptions[file.language] || 'ملف المشروع'
}

function getProjectTypeArabic(type) {
  const types = {
    'react': 'تطبيق React',
    'html': 'موقع HTML',
    'nodejs': 'API Node.js',
    'vue': 'تطبيق Vue.js',
    'angular': 'تطبيق Angular'
  }
  return types[type] || 'مشروع ويب'
}

function getRunInstructions(type) {
  const instructions = {
    'react': `\`\`\`bash
# تثبيت المتطلبات
npm install

# تشغيل المشروع في وضع التطوير
npm start

# بناء المشروع للإنتاج
npm run build
\`\`\``,
    'nodejs': `\`\`\`bash
# تثبيت المتطلبات
npm install

# تشغيل الخادم
npm start

# تشغيل في وضع التطوير
npm run dev
\`\`\``,
    'html': `\`\`\`bash
# افتح ملف index.html في المتصفح
# أو استخدم خادم محلي مثل:
npx serve .
# أو
python -m http.server 8000
\`\`\``
  }
  return instructions[type] || 'افتح ملف index.html في المتصفح'
}

function getRequirements(type) {
  const requirements = {
    'react': '- Node.js (الإصدار 14 أو أحدث)\n- npm أو yarn',
    'nodejs': '- Node.js (الإصدار 14 أو أحدث)\n- npm أو yarn',
    'html': '- متصفح ويب حديث\n- خادم ويب محلي (اختياري)'
  }
  return requirements[type] || 'متصفح ويب حديث'
}

function getDefaultTechnologies(type) {
  const technologies = {
    'react': '- React.js\n- JavaScript (ES6+)\n- HTML5\n- CSS3',
    'nodejs': '- Node.js\n- Express.js\n- JavaScript (ES6+)',
    'html': '- HTML5\n- CSS3\n- JavaScript'
  }
  return technologies[type] || '- HTML5\n- CSS3\n- JavaScript'
}

function generatePackageJson(project) {
  const basePackage = {
    name: project.name.toLowerCase().replace(/\s+/g, '-'),
    version: '1.0.0',
    description: project.description,
    main: 'index.js',
    scripts: {},
    keywords: ['ai-generated', 'project'],
    author: 'AI Project Builder',
    license: 'MIT'
  }

  if (project.type === 'react') {
    basePackage.scripts = {
      start: 'react-scripts start',
      build: 'react-scripts build',
      test: 'react-scripts test',
      eject: 'react-scripts eject'
    }
    basePackage.dependencies = {
      react: '^18.2.0',
      'react-dom': '^18.2.0',
      'react-scripts': '^5.0.1'
    }
    basePackage.browserslist = {
      production: ['>0.2%', 'not dead', 'not op_mini all'],
      development: ['last 1 chrome version', 'last 1 firefox version', 'last 1 safari version']
    }
  } else if (project.type === 'nodejs') {
    basePackage.scripts = {
      start: 'node server.js',
      dev: 'nodemon server.js'
    }
    basePackage.dependencies = {
      express: '^4.18.2',
      cors: '^2.8.5'
    }
    basePackage.devDependencies = {
      nodemon: '^2.0.22'
    }
  }

  return basePackage
}

function generateGitignore(type) {
  const common = `# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs
*.log
`

  const specific = {
    'react': `
# React build
build/
dist/

# Testing
coverage/
`,
    'nodejs': `
# Node.js
dist/
build/
*.tgz
*.tar.gz
`,
    'html': `
# Build files
dist/
build/
`
  }

  return common + (specific[type] || '')
}
