import axios from 'axios'

const API_BASE_URL = '/api'

// Mock AI service for development
const mockProjects = {
  'react todo': {
    name: 'تطبيق إدارة المهام',
    description: 'تطبيق React لإدارة المهام اليومية مع إمكانية الإضافة والحذف والتعديل',
    type: 'react',
    files: [
      {
        name: 'App.jsx',
        path: 'src/App.jsx',
        content: `import React, { useState } from 'react'
import TodoList from './components/TodoList'
import AddTodo from './components/AddTodo'
import './App.css'

function App() {
  const [todos, setTodos] = useState([])

  const addTodo = (text) => {
    const newTodo = {
      id: Date.now(),
      text,
      completed: false,
      createdAt: new Date()
    }
    setTodos([...todos, newTodo])
  }

  const toggleTodo = (id) => {
    setTodos(todos.map(todo =>
      todo.id === id ? { ...todo, completed: !todo.completed } : todo
    ))
  }

  const deleteTodo = (id) => {
    setTodos(todos.filter(todo => todo.id !== id))
  }

  return (
    <div className="app">
      <header className="app-header">
        <h1>مدير المهام</h1>
      </header>
      <main>
        <AddTodo onAdd={addTodo} />
        <TodoList 
          todos={todos} 
          onToggle={toggleTodo} 
          onDelete={deleteTodo} 
        />
      </main>
    </div>
  )
}

export default App`,
        language: 'javascript'
      },
      {
        name: 'TodoList.jsx',
        path: 'src/components/TodoList.jsx',
        content: `import React from 'react'
import TodoItem from './TodoItem'

function TodoList({ todos, onToggle, onDelete }) {
  if (todos.length === 0) {
    return (
      <div className="empty-state">
        <p>لا توجد مهام حالياً</p>
        <p>أضف مهمة جديدة للبدء</p>
      </div>
    )
  }

  return (
    <div className="todo-list">
      {todos.map(todo => (
        <TodoItem
          key={todo.id}
          todo={todo}
          onToggle={onToggle}
          onDelete={onDelete}
        />
      ))}
    </div>
  )
}

export default TodoList`,
        language: 'javascript'
      },
      {
        name: 'TodoItem.jsx',
        path: 'src/components/TodoItem.jsx',
        content: `import React from 'react'

function TodoItem({ todo, onToggle, onDelete }) {
  return (
    <div className={\`todo-item \${todo.completed ? 'completed' : ''}\`}>
      <input
        type="checkbox"
        checked={todo.completed}
        onChange={() => onToggle(todo.id)}
        className="todo-checkbox"
      />
      <span className="todo-text">{todo.text}</span>
      <button
        onClick={() => onDelete(todo.id)}
        className="delete-btn"
      >
        حذف
      </button>
    </div>
  )
}

export default TodoItem`,
        language: 'javascript'
      },
      {
        name: 'AddTodo.jsx',
        path: 'src/components/AddTodo.jsx',
        content: `import React, { useState } from 'react'

function AddTodo({ onAdd }) {
  const [text, setText] = useState('')

  const handleSubmit = (e) => {
    e.preventDefault()
    if (text.trim()) {
      onAdd(text.trim())
      setText('')
    }
  }

  return (
    <form onSubmit={handleSubmit} className="add-todo-form">
      <input
        type="text"
        value={text}
        onChange={(e) => setText(e.target.value)}
        placeholder="أضف مهمة جديدة..."
        className="todo-input"
      />
      <button type="submit" className="add-btn">
        إضافة
      </button>
    </form>
  )
}

export default AddTodo`,
        language: 'javascript'
      },
      {
        name: 'App.css',
        path: 'src/App.css',
        content: `.app {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Cairo', sans-serif;
  direction: rtl;
}

.app-header {
  text-align: center;
  margin-bottom: 30px;
}

.app-header h1 {
  color: #333;
  font-size: 2.5rem;
  margin: 0;
}

.add-todo-form {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
}

.todo-input {
  flex: 1;
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  direction: rtl;
}

.todo-input:focus {
  outline: none;
  border-color: #007bff;
}

.add-btn {
  padding: 12px 24px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
}

.add-btn:hover {
  background: #0056b3;
}

.todo-list {
  space-y: 10px;
}

.todo-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin-bottom: 10px;
}

.todo-item.completed {
  opacity: 0.6;
}

.todo-item.completed .todo-text {
  text-decoration: line-through;
}

.todo-checkbox {
  width: 20px;
  height: 20px;
}

.todo-text {
  flex: 1;
  font-size: 16px;
}

.delete-btn {
  padding: 8px 16px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.delete-btn:hover {
  background: #c82333;
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #666;
}`,
        language: 'css'
      }
    ]
  }
}

// Generate project based on user input
export async function generateProject(userInput) {
  try {
    // For development, use mock data
    if (process.env.NODE_ENV === 'development') {
      return generateMockProject(userInput)
    }

    // In production, call the actual API
    const response = await axios.post(`${API_BASE_URL}/generate`, {
      prompt: userInput
    })

    return {
      success: true,
      ...response.data
    }
  } catch (error) {
    console.error('Error generating project:', error)
    return {
      success: false,
      error: error.message || 'حدث خطأ أثناء إنشاء المشروع'
    }
  }
}

// Mock project generator for development
function generateMockProject(userInput) {
  const input = userInput.toLowerCase()
  
  // Simple keyword matching for demo
  if (input.includes('react') || input.includes('مهام') || input.includes('todo')) {
    const project = mockProjects['react todo']
    
    return {
      success: true,
      description: `تم إنشاء ${project.name} بنجاح! يحتوي المشروع على ${project.files.length} ملفات.`,
      project: {
        id: Date.now().toString(),
        name: project.name,
        description: project.description,
        type: project.type,
        createdAt: new Date().toISOString()
      },
      files: project.files.map(file => ({
        ...file,
        id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      })),
      fileTree: generateFileTree(project.files)
    }
  }

  // Default response for unrecognized input
  return {
    success: true,
    description: 'تم إنشاء مشروع أساسي. يمكنك تعديل الملفات حسب احتياجاتك.',
    project: {
      id: Date.now().toString(),
      name: 'مشروع جديد',
      description: 'مشروع أساسي تم إنشاؤه بواسطة الذكاء الصناعي',
      type: 'basic',
      createdAt: new Date().toISOString()
    },
    files: [
      {
        id: `${Date.now()}-1`,
        name: 'index.html',
        path: 'index.html',
        content: `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مشروع جديد</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>مرحباً بك في مشروعك الجديد!</h1>
        <p>هذا مشروع أساسي تم إنشاؤه بواسطة الذكاء الصناعي.</p>
        <p>يمكنك تعديل هذا الملف وإضافة المزيد من الملفات حسب احتياجاتك.</p>
    </div>
</body>
</html>`,
        language: 'html'
      }
    ],
    fileTree: [
      {
        id: `${Date.now()}-1`,
        name: 'index.html',
        type: 'file',
        path: 'index.html'
      }
    ]
  }
}

// Generate file tree structure
function generateFileTree(files) {
  const tree = []
  const folders = new Map()

  files.forEach(file => {
    const pathParts = file.path.split('/')
    let currentLevel = tree
    let currentPath = ''

    pathParts.forEach((part, index) => {
      currentPath = currentPath ? `${currentPath}/${part}` : part
      
      if (index === pathParts.length - 1) {
        // This is a file
        currentLevel.push({
          id: file.id,
          name: part,
          type: 'file',
          path: file.path,
          language: file.language
        })
      } else {
        // This is a folder
        let folder = currentLevel.find(item => item.name === part && item.type === 'folder')
        if (!folder) {
          folder = {
            id: `folder-${currentPath}`,
            name: part,
            type: 'folder',
            path: currentPath,
            children: []
          }
          currentLevel.push(folder)
        }
        currentLevel = folder.children
      }
    })
  })

  return tree
}
