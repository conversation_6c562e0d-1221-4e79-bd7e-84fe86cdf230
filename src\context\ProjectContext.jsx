import React, { createContext, useContext, useReducer, useCallback } from 'react'
import { v4 as uuidv4 } from 'uuid'

const ProjectContext = createContext()

// Initial state
const initialState = {
  project: null,
  files: {},
  fileTree: [],
  chatMessages: [],
  isGenerating: false,
  error: null,
}

// Action types
const actionTypes = {
  SET_PROJECT: 'SET_PROJECT',
  ADD_FILE: 'ADD_FILE',
  UPDATE_FILE: 'UPDATE_FILE',
  DELETE_FILE: 'DELETE_FILE',
  SET_FILE_TREE: 'SET_FILE_TREE',
  ADD_CHAT_MESSAGE: 'ADD_CHAT_MESSAGE',
  SET_GENERATING: 'SET_GENERATING',
  SET_ERROR: 'SET_ERROR',
  CLEAR_PROJECT: 'CLEAR_PROJECT',
}

// Reducer
function projectReducer(state, action) {
  switch (action.type) {
    case actionTypes.SET_PROJECT:
      return {
        ...state,
        project: action.payload,
        error: null,
      }

    case actionTypes.ADD_FILE:
      return {
        ...state,
        files: {
          ...state.files,
          [action.payload.id]: action.payload,
        },
      }

    case actionTypes.UPDATE_FILE:
      return {
        ...state,
        files: {
          ...state.files,
          [action.payload.id]: {
            ...state.files[action.payload.id],
            ...action.payload.updates,
          },
        },
      }

    case actionTypes.DELETE_FILE:
      const newFiles = { ...state.files }
      delete newFiles[action.payload]
      return {
        ...state,
        files: newFiles,
      }

    case actionTypes.SET_FILE_TREE:
      return {
        ...state,
        fileTree: action.payload,
      }

    case actionTypes.ADD_CHAT_MESSAGE:
      return {
        ...state,
        chatMessages: [...state.chatMessages, action.payload],
      }

    case actionTypes.SET_GENERATING:
      return {
        ...state,
        isGenerating: action.payload,
      }

    case actionTypes.SET_ERROR:
      return {
        ...state,
        error: action.payload,
      }

    case actionTypes.CLEAR_PROJECT:
      return initialState

    default:
      return state
  }
}

// Provider component
export function ProjectProvider({ children }) {
  const [state, dispatch] = useReducer(projectReducer, initialState)

  // Actions
  const setProject = useCallback((project) => {
    dispatch({ type: actionTypes.SET_PROJECT, payload: project })
  }, [])

  const addFile = useCallback((file) => {
    const fileWithId = {
      id: uuidv4(),
      name: file.name,
      path: file.path,
      content: file.content || '',
      type: file.type || 'file',
      language: file.language || 'javascript',
      ...file,
    }
    dispatch({ type: actionTypes.ADD_FILE, payload: fileWithId })
    return fileWithId
  }, [])

  const updateFile = useCallback((id, updates) => {
    dispatch({ type: actionTypes.UPDATE_FILE, payload: { id, updates } })
  }, [])

  const deleteFile = useCallback((id) => {
    dispatch({ type: actionTypes.DELETE_FILE, payload: id })
  }, [])

  const setFileTree = useCallback((tree) => {
    dispatch({ type: actionTypes.SET_FILE_TREE, payload: tree })
  }, [])

  const addChatMessage = useCallback((message) => {
    const messageWithId = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      ...message,
    }
    dispatch({ type: actionTypes.ADD_CHAT_MESSAGE, payload: messageWithId })
    return messageWithId
  }, [])

  const setGenerating = useCallback((isGenerating) => {
    dispatch({ type: actionTypes.SET_GENERATING, payload: isGenerating })
  }, [])

  const setError = useCallback((error) => {
    dispatch({ type: actionTypes.SET_ERROR, payload: error })
  }, [])

  const clearProject = useCallback(() => {
    dispatch({ type: actionTypes.CLEAR_PROJECT })
  }, [])

  const value = {
    ...state,
    setProject,
    addFile,
    updateFile,
    deleteFile,
    setFileTree,
    addChatMessage,
    setGenerating,
    setError,
    clearProject,
  }

  return (
    <ProjectContext.Provider value={value}>
      {children}
    </ProjectContext.Provider>
  )
}

// Hook to use the context
export function useProject() {
  const context = useContext(ProjectContext)
  if (!context) {
    throw new Error('useProject must be used within a ProjectProvider')
  }
  return context
}
