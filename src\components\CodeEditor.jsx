import React, { useRef, useEffect, useState } from 'react'
import Editor from '@monaco-editor/react'
import { 
  FiSave, 
  FiCopy, 
  FiMaximize2, 
  FiMinimize2,
  FiSettings,
  FiCode
} from 'react-icons/fi'
import { useProject } from '../context/ProjectContext'

function CodeEditor({ activeFile }) {
  const editorRef = useRef(null)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [fontSize, setFontSize] = useState(14)
  const [theme, setTheme] = useState('vs-dark')
  const [showSettings, setShowSettings] = useState(false)
  const { updateFile } = useProject()

  const handleEditorDidMount = (editor, monaco) => {
    editorRef.current = editor

    // Configure editor options
    editor.updateOptions({
      fontSize: fontSize,
      fontFamily: 'Fira Code, Monaco, Consolas, monospace',
      fontLigatures: true,
      minimap: { enabled: true },
      scrollBeyondLastLine: false,
      automaticLayout: true,
      wordWrap: 'on',
      lineNumbers: 'on',
      renderWhitespace: 'selection',
      bracketPairColorization: { enabled: true },
      guides: {
        bracketPairs: true,
        indentation: true
      }
    })

    // Add keyboard shortcuts
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      handleSave()
    })

    // Configure language features
    monaco.languages.typescript.javascriptDefaults.setDiagnosticsOptions({
      noSemanticValidation: false,
      noSyntaxValidation: false,
    })

    monaco.languages.typescript.javascriptDefaults.setCompilerOptions({
      target: monaco.languages.typescript.ScriptTarget.ES2020,
      allowNonTsExtensions: true,
      moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
      module: monaco.languages.typescript.ModuleKind.CommonJS,
      noEmit: true,
      esModuleInterop: true,
      jsx: monaco.languages.typescript.JsxEmit.React,
      reactNamespace: 'React',
      allowJs: true,
      typeRoots: ['node_modules/@types']
    })
  }

  const handleEditorChange = (value) => {
    if (activeFile && value !== undefined) {
      updateFile(activeFile.id, { content: value })
    }
  }

  const handleSave = () => {
    if (activeFile && editorRef.current) {
      const content = editorRef.current.getValue()
      updateFile(activeFile.id, { content })
      // Show save feedback
      const saveButton = document.querySelector('.save-button')
      if (saveButton) {
        saveButton.classList.add('saved')
        setTimeout(() => {
          saveButton.classList.remove('saved')
        }, 1000)
      }
    }
  }

  const handleCopyCode = () => {
    if (editorRef.current) {
      const content = editorRef.current.getValue()
      navigator.clipboard.writeText(content).then(() => {
        // Show copy feedback
        const copyButton = document.querySelector('.copy-button')
        if (copyButton) {
          copyButton.classList.add('copied')
          setTimeout(() => {
            copyButton.classList.remove('copied')
          }, 1000)
        }
      })
    }
  }

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen)
  }

  const handleFontSizeChange = (newSize) => {
    setFontSize(newSize)
    if (editorRef.current) {
      editorRef.current.updateOptions({ fontSize: newSize })
    }
  }

  const handleThemeChange = (newTheme) => {
    setTheme(newTheme)
  }

  useEffect(() => {
    if (editorRef.current && activeFile) {
      // Set the language based on file extension
      const model = editorRef.current.getModel()
      if (model) {
        const monaco = window.monaco
        if (monaco) {
          monaco.editor.setModelLanguage(model, activeFile.language || 'javascript')
        }
      }
    }
  }, [activeFile])

  if (!activeFile) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-50">
        <div className="text-center text-gray-500">
          <FiCode className="w-16 h-16 mx-auto mb-4 text-gray-300" />
          <h3 className="text-lg font-medium mb-2">لم يتم اختيار ملف</h3>
          <p className="text-sm">اختر ملفاً من شجرة الملفات لبدء التحرير</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`h-full flex flex-col bg-white ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <FiCode className="w-4 h-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-800">{activeFile.name}</span>
          </div>
          <div className="text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded">
            {activeFile.language}
          </div>
        </div>

        <div className="flex items-center gap-1">
          {/* Save Button */}
          <button
            onClick={handleSave}
            className="save-button p-2 hover:bg-gray-200 rounded text-gray-600 hover:text-gray-800 transition-colors"
            title="حفظ (Ctrl+S)"
          >
            <FiSave className="w-4 h-4" />
          </button>

          {/* Copy Button */}
          <button
            onClick={handleCopyCode}
            className="copy-button p-2 hover:bg-gray-200 rounded text-gray-600 hover:text-gray-800 transition-colors"
            title="نسخ الكود"
          >
            <FiCopy className="w-4 h-4" />
          </button>

          {/* Settings Button */}
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="p-2 hover:bg-gray-200 rounded text-gray-600 hover:text-gray-800 transition-colors"
            title="إعدادات المحرر"
          >
            <FiSettings className="w-4 h-4" />
          </button>

          {/* Fullscreen Button */}
          <button
            onClick={toggleFullscreen}
            className="p-2 hover:bg-gray-200 rounded text-gray-600 hover:text-gray-800 transition-colors"
            title={isFullscreen ? 'تصغير' : 'ملء الشاشة'}
          >
            {isFullscreen ? <FiMinimize2 className="w-4 h-4" /> : <FiMaximize2 className="w-4 h-4" />}
          </button>
        </div>
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="p-3 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center gap-4 text-sm">
            <div className="flex items-center gap-2">
              <label className="text-gray-600">حجم الخط:</label>
              <select
                value={fontSize}
                onChange={(e) => handleFontSizeChange(Number(e.target.value))}
                className="border border-gray-300 rounded px-2 py-1"
              >
                <option value={12}>12px</option>
                <option value={14}>14px</option>
                <option value={16}>16px</option>
                <option value={18}>18px</option>
                <option value={20}>20px</option>
              </select>
            </div>

            <div className="flex items-center gap-2">
              <label className="text-gray-600">المظهر:</label>
              <select
                value={theme}
                onChange={(e) => handleThemeChange(e.target.value)}
                className="border border-gray-300 rounded px-2 py-1"
              >
                <option value="vs">فاتح</option>
                <option value="vs-dark">داكن</option>
                <option value="hc-black">تباين عالي</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Editor */}
      <div className="flex-1">
        <Editor
          height="100%"
          language={activeFile.language || 'javascript'}
          value={activeFile.content || ''}
          theme={theme}
          onChange={handleEditorChange}
          onMount={handleEditorDidMount}
          options={{
            fontSize: fontSize,
            fontFamily: 'Fira Code, Monaco, Consolas, monospace',
            fontLigatures: true,
            minimap: { enabled: true },
            scrollBeyondLastLine: false,
            automaticLayout: true,
            wordWrap: 'on',
            lineNumbers: 'on',
            renderWhitespace: 'selection',
            bracketPairColorization: { enabled: true },
            guides: {
              bracketPairs: true,
              indentation: true
            },
            suggestOnTriggerCharacters: true,
            acceptSuggestionOnEnter: 'on',
            tabCompletion: 'on',
            quickSuggestions: true,
            parameterHints: { enabled: true },
            formatOnPaste: true,
            formatOnType: true
          }}
        />
      </div>

      <style jsx>{`
        .save-button.saved {
          color: #10b981 !important;
          background-color: #d1fae5 !important;
        }
        
        .copy-button.copied {
          color: #3b82f6 !important;
          background-color: #dbeafe !important;
        }
      `}</style>
    </div>
  )
}

export default CodeEditor
