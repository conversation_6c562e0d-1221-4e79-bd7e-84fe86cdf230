import React, { useState, useRef, useEffect } from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Loader } from 'react-icons/fi'
import { useProject } from '../context/ProjectContext'
import { generateProject } from '../services/aiService'

function ChatPanel() {
  const [message, setMessage] = useState('')
  const messagesEndRef = useRef(null)
  const inputRef = useRef(null)
  
  const { 
    chatMessages, 
    addChatMessage, 
    isGenerating, 
    setGenerating,
    setProject,
    setFileTree,
    addFile,
    setError
  } = useProject()

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [chatMessages])

  const handleSubmit = async (e) => {
    e.preventDefault()
    if (!message.trim() || isGenerating) return

    const userMessage = message.trim()
    setMessage('')

    // Add user message
    addChatMessage({
      type: 'user',
      content: userMessage,
    })

    // Start generating
    setGenerating(true)

    try {
      // Add AI thinking message
      const thinkingMessage = addChatMessage({
        type: 'ai',
        content: 'جاري تحليل طلبك وإنشاء المشروع...',
        isThinking: true,
      })

      // Generate project
      const result = await generateProject(userMessage)
      
      if (result.success) {
        // Update thinking message with result
        addChatMessage({
          type: 'ai',
          content: result.description,
          project: result.project,
        })

        // Set project data
        setProject(result.project)
        setFileTree(result.fileTree)

        // Add files to context
        result.files.forEach(file => {
          addFile(file)
        })
      } else {
        addChatMessage({
          type: 'ai',
          content: `عذراً، حدث خطأ أثناء إنشاء المشروع: ${result.error}`,
          isError: true,
        })
        setError(result.error)
      }
    } catch (error) {
      console.error('Error generating project:', error)
      addChatMessage({
        type: 'ai',
        content: 'عذراً، حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.',
        isError: true,
      })
      setError(error.message)
    } finally {
      setGenerating(false)
    }
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
          <FiCpu className="w-5 h-5 text-blue-500" />
          مساعد الذكاء الصناعي
        </h2>
        <p className="text-sm text-gray-600 mt-1">
          اكتب فكرة مشروعك وسأقوم بإنشائه لك
        </p>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {chatMessages.length === 0 && (
          <div className="text-center text-gray-500 py-8">
            <FiCpu className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p className="text-lg font-medium mb-2">مرحباً! كيف يمكنني مساعدتك؟</p>
            <p className="text-sm">
              اكتب وصفاً لمشروعك وسأقوم بإنشاء الكود والملفات اللازمة
            </p>
            <div className="mt-4 text-xs text-gray-400 space-y-1">
              <p>أمثلة:</p>
              <p>• "أنشئ لي تطبيق لإدارة المهام باستخدام React"</p>
              <p>• "أريد API بسيط بـNode.js وExpress"</p>
              <p>• "موقع شخصي بـHTML وCSS وJavaScript"</p>
            </div>
          </div>
        )}

        {chatMessages.map((msg) => (
          <div
            key={msg.id}
            className={`chat-message flex gap-3 ${
              msg.type === 'user' ? 'flex-row-reverse' : 'flex-row'
            }`}
          >
            {/* Avatar */}
            <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
              msg.type === 'user' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-600'
            }`}>
              {msg.type === 'user' ? <FiUser className="w-4 h-4" /> : <FiCpu className="w-4 h-4" />}
            </div>

            {/* Message */}
            <div className={`max-w-[80%] ${
              msg.type === 'user' ? 'text-right' : 'text-right'
            }`}>
              <div className={`rounded-lg px-4 py-2 ${
                msg.type === 'user'
                  ? 'bg-blue-500 text-white'
                  : msg.isError
                  ? 'bg-red-50 text-red-700 border border-red-200'
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {msg.isThinking && (
                  <div className="flex items-center gap-2 mb-2">
                    <div className="spinner"></div>
                    <span className="text-sm">جاري التفكير...</span>
                  </div>
                )}
                <p className="whitespace-pre-wrap">{msg.content}</p>
                
                {msg.project && (
                  <div className="mt-3 p-3 bg-white bg-opacity-20 rounded border">
                    <p className="text-sm font-medium mb-1">تم إنشاء المشروع:</p>
                    <p className="text-sm">{msg.project.name}</p>
                    <p className="text-xs mt-1 opacity-75">{msg.project.description}</p>
                  </div>
                )}
              </div>
              
              <div className="text-xs text-gray-500 mt-1">
                {new Date(msg.timestamp).toLocaleTimeString('ar-SA')}
              </div>
            </div>
          </div>
        ))}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t border-gray-200">
        <form onSubmit={handleSubmit} className="flex gap-2">
          <textarea
            ref={inputRef}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="اكتب فكرة مشروعك هنا..."
            className="flex-1 resize-none border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            rows="2"
            disabled={isGenerating}
          />
          <button
            type="submit"
            disabled={!message.trim() || isGenerating}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
          >
            {isGenerating ? (
              <FiLoader className="w-4 h-4 animate-spin" />
            ) : (
              <FiSend className="w-4 h-4" />
            )}
          </button>
        </form>
      </div>
    </div>
  )
}

export default ChatPanel
