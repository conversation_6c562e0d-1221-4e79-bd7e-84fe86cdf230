import express from 'express'
import cors from 'cors'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'
import { generateProjectWithAI } from './services/aiService.js'
import { createZipArchive } from './services/exportService.js'
import { uploadToGitHub } from './services/githubService.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

const app = express()
const PORT = process.env.PORT || 5000

// Middleware
app.use(cors())
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// Serve static files from the React app
app.use(express.static(join(__dirname, '../dist')))

// API Routes

// Generate project using AI
app.post('/api/generate', async (req, res) => {
  try {
    const { prompt } = req.body

    if (!prompt) {
      return res.status(400).json({
        success: false,
        error: 'يرجى تقديم وصف للمشروع'
      })
    }

    console.log('Generating project for prompt:', prompt)

    const result = await generateProjectWithAI(prompt)

    if (result.success) {
      res.json({
        success: true,
        description: result.description,
        project: result.project,
        files: result.files,
        fileTree: result.fileTree
      })
    } else {
      res.status(500).json({
        success: false,
        error: result.error
      })
    }
  } catch (error) {
    console.error('Error generating project:', error)
    res.status(500).json({
      success: false,
      error: 'حدث خطأ في الخادم أثناء إنشاء المشروع'
    })
  }
})

// Export project as ZIP
app.post('/api/export/zip', async (req, res) => {
  try {
    const { project, files } = req.body

    if (!project || !files) {
      return res.status(400).json({
        success: false,
        error: 'بيانات المشروع غير مكتملة'
      })
    }

    console.log('Creating ZIP for project:', project.name)

    const zipBuffer = await createZipArchive(project, files)

    res.setHeader('Content-Type', 'application/zip')
    res.setHeader('Content-Disposition', `attachment; filename="${project.name || 'project'}.zip"`)
    res.send(zipBuffer)
  } catch (error) {
    console.error('Error creating ZIP:', error)
    res.status(500).json({
      success: false,
      error: 'حدث خطأ أثناء إنشاء ملف ZIP'
    })
  }
})

// Export project to GitHub
app.post('/api/export/github', async (req, res) => {
  try {
    const { project, files, githubToken, repoName } = req.body

    if (!project || !files || !githubToken || !repoName) {
      return res.status(400).json({
        success: false,
        error: 'بيانات غير مكتملة للرفع إلى GitHub'
      })
    }

    console.log('Uploading to GitHub:', repoName)

    const result = await uploadToGitHub(project, files, githubToken, repoName)

    if (result.success) {
      res.json({
        success: true,
        repoUrl: result.repoUrl,
        message: 'تم رفع المشروع إلى GitHub بنجاح'
      })
    } else {
      res.status(500).json({
        success: false,
        error: result.error
      })
    }
  } catch (error) {
    console.error('Error uploading to GitHub:', error)
    res.status(500).json({
      success: false,
      error: 'حدث خطأ أثناء الرفع إلى GitHub'
    })
  }
})

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Server is running',
    timestamp: new Date().toISOString()
  })
})

// Catch all handler: send back React's index.html file
app.get('*', (req, res) => {
  res.sendFile(join(__dirname, '../dist/index.html'))
})

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Server error:', error)
  res.status(500).json({
    success: false,
    error: 'حدث خطأ في الخادم'
  })
})

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Server is running on port ${PORT}`)
  console.log(`📱 Frontend: http://localhost:${PORT}`)
  console.log(`🔧 API: http://localhost:${PORT}/api`)
})
