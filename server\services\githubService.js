import axios from 'axios'

export async function uploadToGitHub(project, files, githubToken, repoName) {
  try {
    const username = await getGitHubUsername(githubToken)
    
    // Create repository
    const repoResult = await createRepository(githubToken, repoName, project.description)
    
    if (!repoResult.success) {
      return repoResult
    }

    // Upload files to repository
    const uploadResult = await uploadFiles(githubToken, username, repoName, project, files)
    
    if (!uploadResult.success) {
      return uploadResult
    }

    return {
      success: true,
      repoUrl: `https://github.com/${username}/${repoName}`,
      message: 'تم رفع المشروع إلى GitHub بنجاح'
    }

  } catch (error) {
    console.error('GitHub upload error:', error)
    return {
      success: false,
      error: error.message || 'حدث خطأ أثناء الرفع إلى GitHub'
    }
  }
}

async function getGitHubUsername(token) {
  try {
    const response = await axios.get('https://api.github.com/user', {
      headers: {
        'Authorization': `token ${token}`,
        'Accept': 'application/vnd.github.v3+json'
      }
    })
    return response.data.login
  } catch (error) {
    throw new Error('فشل في الحصول على معلومات المستخدم من GitHub')
  }
}

async function createRepository(token, repoName, description) {
  try {
    const response = await axios.post('https://api.github.com/user/repos', {
      name: repoName,
      description: description || 'مشروع تم إنشاؤه بواسطة مولد المشاريع الذكي',
      private: false,
      auto_init: false,
      has_issues: true,
      has_projects: true,
      has_wiki: true
    }, {
      headers: {
        'Authorization': `token ${token}`,
        'Accept': 'application/vnd.github.v3+json',
        'Content-Type': 'application/json'
      }
    })

    return {
      success: true,
      repo: response.data
    }
  } catch (error) {
    if (error.response?.status === 422) {
      // Repository already exists
      return {
        success: false,
        error: 'مستودع بهذا الاسم موجود بالفعل. يرجى اختيار اسم آخر.'
      }
    }
    
    console.error('Repository creation error:', error.response?.data)
    return {
      success: false,
      error: 'فشل في إنشاء المستودع على GitHub'
    }
  }
}

async function uploadFiles(token, username, repoName, project, files) {
  try {
    // Create all files
    const filePromises = []

    // Add project files
    Object.values(files).forEach(file => {
      if (file.content) {
        filePromises.push(
          createFile(token, username, repoName, file.path, file.content, `Add ${file.name}`)
        )
      }
    })

    // Add README.md
    const readmeContent = generateReadmeForGitHub(project, files)
    filePromises.push(
      createFile(token, username, repoName, 'README.md', readmeContent, 'Add README.md')
    )

    // Add .gitignore
    const gitignoreContent = generateGitignore(project.type)
    filePromises.push(
      createFile(token, username, repoName, '.gitignore', gitignoreContent, 'Add .gitignore')
    )

    // Add package.json if needed
    if ((project.type === 'nodejs' || project.type === 'react') && 
        !Object.values(files).some(file => file.name === 'package.json')) {
      const packageJson = generatePackageJson(project)
      filePromises.push(
        createFile(token, username, repoName, 'package.json', 
                  JSON.stringify(packageJson, null, 2), 'Add package.json')
      )
    }

    // Execute all file uploads with delay to avoid rate limiting
    for (const promise of filePromises) {
      await promise
      // Add delay between requests to respect GitHub API rate limits
      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    return { success: true }

  } catch (error) {
    console.error('File upload error:', error)
    return {
      success: false,
      error: 'فشل في رفع الملفات إلى GitHub'
    }
  }
}

async function createFile(token, username, repoName, path, content, message) {
  try {
    // Encode content to base64
    const encodedContent = Buffer.from(content, 'utf8').toString('base64')

    const response = await axios.put(
      `https://api.github.com/repos/${username}/${repoName}/contents/${path}`,
      {
        message: message,
        content: encodedContent,
        branch: 'main'
      },
      {
        headers: {
          'Authorization': `token ${token}`,
          'Accept': 'application/vnd.github.v3+json',
          'Content-Type': 'application/json'
        }
      }
    )

    return response.data
  } catch (error) {
    console.error(`Error creating file ${path}:`, error.response?.data)
    throw error
  }
}

function generateReadmeForGitHub(project, files) {
  const filesList = Object.values(files)
    .map(file => `- [\`${file.path}\`](./${file.path}) - ${getFileDescription(file)}`)
    .join('\n')

  return `# ${project.name}

${project.description}

## 📋 وصف المشروع

هذا المشروع تم إنشاؤه بواسطة **[مولد المشاريع الذكي](https://github.com/your-username/ai-project-builder)** باستخدام الذكاء الصناعي.

- **نوع المشروع:** ${getProjectTypeArabic(project.type)}
- **تاريخ الإنشاء:** ${new Date(project.createdAt).toLocaleDateString('ar-SA')}

## 📁 هيكل الملفات

${filesList}

## 🚀 كيفية التشغيل

${getRunInstructions(project.type)}

## 📋 المتطلبات

${getRequirements(project.type)}

## ✨ الميزات

${project.features ? project.features.map(feature => `- ${feature}`).join('\n') : '- تم إنشاؤه بواسطة الذكاء الصناعي\n- كود جاهز للتشغيل\n- هيكل مشروع منظم'}

## 🛠️ التقنيات المستخدمة

${project.technologies ? project.technologies.map(tech => `- ${tech}`).join('\n') : getDefaultTechnologies(project.type)}

## 🤝 المساهمة

يمكنك المساهمة في تطوير هذا المشروع عن طريق:

1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة الجديدة (\`git checkout -b feature/AmazingFeature\`)
3. إضافة التحسينات (\`git commit -m 'Add some AmazingFeature'\`)
4. رفع التغييرات (\`git push origin feature/AmazingFeature\`)
5. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🙏 شكر وتقدير

- تم إنشاء هذا المشروع بواسطة [مولد المشاريع الذكي](https://github.com/your-username/ai-project-builder)
- شكر خاص للذكاء الصناعي في مساعدة المطورين

---

⭐ إذا أعجبك هذا المشروع، لا تنس إعطاؤه نجمة!
`
}

// Helper functions (reuse from exportService.js)
function getFileDescription(file) {
  const descriptions = {
    'html': 'ملف HTML رئيسي',
    'css': 'ملف أنماط CSS',
    'javascript': 'ملف JavaScript',
    'json': 'ملف تكوين JSON',
    'markdown': 'ملف توثيق Markdown',
    'text': 'ملف نصي'
  }
  return descriptions[file.language] || 'ملف المشروع'
}

function getProjectTypeArabic(type) {
  const types = {
    'react': 'تطبيق React',
    'html': 'موقع HTML',
    'nodejs': 'API Node.js',
    'vue': 'تطبيق Vue.js',
    'angular': 'تطبيق Angular'
  }
  return types[type] || 'مشروع ويب'
}

function getRunInstructions(type) {
  const instructions = {
    'react': `\`\`\`bash
# تثبيت المتطلبات
npm install

# تشغيل المشروع في وضع التطوير
npm start

# بناء المشروع للإنتاج
npm run build
\`\`\``,
    'nodejs': `\`\`\`bash
# تثبيت المتطلبات
npm install

# تشغيل الخادم
npm start

# تشغيل في وضع التطوير
npm run dev
\`\`\``,
    'html': `\`\`\`bash
# افتح ملف index.html في المتصفح
# أو استخدم خادم محلي مثل:
npx serve .
# أو
python -m http.server 8000
\`\`\``
  }
  return instructions[type] || 'افتح ملف index.html في المتصفح'
}

function getRequirements(type) {
  const requirements = {
    'react': '- Node.js (الإصدار 14 أو أحدث)\n- npm أو yarn',
    'nodejs': '- Node.js (الإصدار 14 أو أحدث)\n- npm أو yarn',
    'html': '- متصفح ويب حديث\n- خادم ويب محلي (اختياري)'
  }
  return requirements[type] || 'متصفح ويب حديث'
}

function getDefaultTechnologies(type) {
  const technologies = {
    'react': '- React.js\n- JavaScript (ES6+)\n- HTML5\n- CSS3',
    'nodejs': '- Node.js\n- Express.js\n- JavaScript (ES6+)',
    'html': '- HTML5\n- CSS3\n- JavaScript'
  }
  return technologies[type] || '- HTML5\n- CSS3\n- JavaScript'
}

function generatePackageJson(project) {
  const basePackage = {
    name: project.name.toLowerCase().replace(/\s+/g, '-'),
    version: '1.0.0',
    description: project.description,
    main: 'index.js',
    scripts: {},
    keywords: ['ai-generated', 'project'],
    author: 'AI Project Builder',
    license: 'MIT'
  }

  if (project.type === 'react') {
    basePackage.scripts = {
      start: 'react-scripts start',
      build: 'react-scripts build',
      test: 'react-scripts test',
      eject: 'react-scripts eject'
    }
    basePackage.dependencies = {
      react: '^18.2.0',
      'react-dom': '^18.2.0',
      'react-scripts': '^5.0.1'
    }
  } else if (project.type === 'nodejs') {
    basePackage.scripts = {
      start: 'node server.js',
      dev: 'nodemon server.js'
    }
    basePackage.dependencies = {
      express: '^4.18.2',
      cors: '^2.8.5'
    }
  }

  return basePackage
}

function generateGitignore(type) {
  const common = `# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs
*.log
`

  const specific = {
    'react': `
# React build
build/
dist/

# Testing
coverage/
`,
    'nodejs': `
# Node.js
dist/
build/
*.tgz
*.tar.gz
`,
    'html': `
# Build files
dist/
build/
`
  }

  return common + (specific[type] || '')
}
