import React, { useState, useEffect } from 'react'
import { 
  FiX, 
  FiKey, 
  FiExternalLink, 
  FiCheck, 
  FiAlertCircle,
  FiEye,
  FiEyeOff
} from 'react-icons/fi'

function ApiKeyModal({ isOpen, onClose, onSave }) {
  const [apiKey, setApiKey] = useState('')
  const [showKey, setShowKey] = useState(false)
  const [isValidating, setIsValidating] = useState(false)
  const [validationResult, setValidationResult] = useState(null)

  useEffect(() => {
    // Load saved API key from localStorage
    const savedKey = localStorage.getItem('openai_api_key')
    if (savedKey) {
      setApiKey(savedKey)
    }
  }, [isOpen])

  const validateApiKey = async (key) => {
    if (!key || key.length < 20) {
      return { valid: false, error: 'مفتاح API غير صحيح' }
    }

    try {
      // Simple validation - check if key format is correct
      if (!key.startsWith('sk-')) {
        return { valid: false, error: 'مفتاح API يجب أن يبدأ بـ sk-' }
      }

      return { valid: true }
    } catch (error) {
      return { valid: false, error: 'فشل في التحقق من المفتاح' }
    }
  }

  const handleSave = async () => {
    if (!apiKey.trim()) {
      setValidationResult({ valid: false, error: 'يرجى إدخال مفتاح API' })
      return
    }

    setIsValidating(true)
    setValidationResult(null)

    const validation = await validateApiKey(apiKey.trim())
    
    if (validation.valid) {
      // Save to localStorage
      localStorage.setItem('openai_api_key', apiKey.trim())
      
      // Call parent callback
      onSave(apiKey.trim())
      
      setValidationResult({ valid: true, message: 'تم حفظ المفتاح بنجاح' })
      
      // Close modal after short delay
      setTimeout(() => {
        onClose()
        setValidationResult(null)
      }, 1500)
    } else {
      setValidationResult(validation)
    }

    setIsValidating(false)
  }

  const handleClose = () => {
    setValidationResult(null)
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center gap-2">
            <FiKey className="w-5 h-5 text-blue-500" />
            <h2 className="text-lg font-semibold text-gray-800">إعداد مفتاح OpenAI API</h2>
          </div>
          <button
            onClick={handleClose}
            className="p-1 hover:bg-gray-100 rounded-full transition-colors"
          >
            <FiX className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-4 space-y-4">
          <div className="text-sm text-gray-600">
            <p className="mb-2">لاستخدام الذكاء الصناعي الحقيقي، تحتاج إلى مفتاح OpenAI API:</p>
            <ol className="list-decimal list-inside space-y-1 text-xs">
              <li>انتقل إلى موقع OpenAI</li>
              <li>سجل الدخول أو أنشئ حساب جديد</li>
              <li>اذهب إلى صفحة API Keys</li>
              <li>أنشئ مفتاح جديد وانسخه</li>
            </ol>
          </div>

          {/* API Key Input */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              مفتاح OpenAI API
            </label>
            <div className="relative">
              <input
                type={showKey ? 'text' : 'password'}
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                placeholder="sk-..."
                className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm font-mono"
                dir="ltr"
              />
              <button
                type="button"
                onClick={() => setShowKey(!showKey)}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                {showKey ? <FiEyeOff className="w-4 h-4" /> : <FiEye className="w-4 h-4" />}
              </button>
            </div>
          </div>

          {/* Get API Key Link */}
          <div className="text-center">
            <a
              href="https://platform.openai.com/api-keys"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800 hover:underline"
            >
              <FiExternalLink className="w-4 h-4" />
              الحصول على مفتاح API من OpenAI
            </a>
          </div>

          {/* Validation Result */}
          {validationResult && (
            <div className={`p-3 rounded-lg flex items-start gap-2 ${
              validationResult.valid 
                ? 'bg-green-50 text-green-700 border border-green-200' 
                : 'bg-red-50 text-red-700 border border-red-200'
            }`}>
              {validationResult.valid ? (
                <FiCheck className="w-4 h-4 mt-0.5 flex-shrink-0" />
              ) : (
                <FiAlertCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
              )}
              <div className="flex-1">
                <p className="text-sm font-medium">
                  {validationResult.valid ? 'نجح!' : 'خطأ'}
                </p>
                <p className="text-xs mt-1">
                  {validationResult.message || validationResult.error}
                </p>
              </div>
            </div>
          )}

          {/* Security Note */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div className="flex items-start gap-2">
              <FiAlertCircle className="w-4 h-4 text-yellow-600 mt-0.5 flex-shrink-0" />
              <div className="text-xs text-yellow-800">
                <p className="font-medium mb-1">ملاحظة أمنية:</p>
                <p>سيتم حفظ المفتاح محلياً في متصفحك فقط. لن يتم إرساله إلى أي خادم خارجي.</p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-2 p-4 border-t border-gray-200">
          <button
            onClick={handleClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            إلغاء
          </button>
          <button
            onClick={handleSave}
            disabled={isValidating || !apiKey.trim()}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
          >
            {isValidating ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                جاري التحقق...
              </>
            ) : (
              <>
                <FiCheck className="w-4 h-4" />
                حفظ
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  )
}

export default ApiKeyModal
