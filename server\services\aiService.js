import OpenAI from 'openai'

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

// Project templates for different types
const projectTemplates = {
  react: {
    name: 'React Application',
    description: 'Modern React application with components',
    files: [
      {
        name: 'package.json',
        path: 'package.json',
        language: 'json',
        template: true
      },
      {
        name: 'index.html',
        path: 'public/index.html',
        language: 'html',
        template: true
      },
      {
        name: 'App.jsx',
        path: 'src/App.jsx',
        language: 'javascript',
        template: true
      }
    ]
  },
  html: {
    name: 'HTML Website',
    description: 'Static HTML website with CSS and JavaScript',
    files: [
      {
        name: 'index.html',
        path: 'index.html',
        language: 'html',
        template: true
      },
      {
        name: 'style.css',
        path: 'css/style.css',
        language: 'css',
        template: true
      },
      {
        name: 'script.js',
        path: 'js/script.js',
        language: 'javascript',
        template: true
      }
    ]
  },
  nodejs: {
    name: 'Node.js API',
    description: 'RESTful API built with Node.js and Express',
    files: [
      {
        name: 'package.json',
        path: 'package.json',
        language: 'json',
        template: true
      },
      {
        name: 'server.js',
        path: 'server.js',
        language: 'javascript',
        template: true
      },
      {
        name: 'routes.js',
        path: 'routes/index.js',
        language: 'javascript',
        template: true
      }
    ]
  }
}

export async function generateProjectWithAI(prompt) {
  try {
    // Check for API key
    if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY === 'your_openai_api_key_here') {
      console.log('⚠️  No valid OpenAI API key found. Please add your API key to .env file')
      return {
        success: false,
        error: 'يرجى إضافة مفتاح OpenAI API صحيح في ملف .env للحصول على توليد أكواد حقيقية'
      }
    }

    console.log('🤖 Using OpenAI API to generate real project...')

    // Step 1: Analyze the prompt to determine project structure
    const analysisPrompt = `
أنت مطور خبير. حلل الطلب التالي وحدد نوع المشروع والملفات المطلوبة:

الطلب: "${prompt}"

يرجى الرد بتنسيق JSON صحيح فقط:
{
  "projectType": "react|html|nodejs|vue|angular|python|java",
  "projectName": "اسم المشروع بالعربية",
  "description": "وصف مختصر للمشروع",
  "features": ["ميزة 1", "ميزة 2", "ميزة 3"],
  "technologies": ["تقنية 1", "تقنية 2"],
  "files": [
    {
      "name": "اسم الملف",
      "path": "مسار/الملف",
      "language": "html|css|javascript|python|java|json|markdown",
      "description": "وصف الملف"
    }
  ]
}
`

    const analysisResponse = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: "أنت مطور خبير متخصص في تحليل متطلبات المشاريع البرمجية. ترد بـ JSON صحيح فقط."
        },
        {
          role: "user",
          content: analysisPrompt
        }
      ],
      temperature: 0.2,
      max_tokens: 1000
    })

    let projectAnalysis
    try {
      const responseContent = analysisResponse.choices[0].message.content.trim()
      // Extract JSON from response (in case there's extra text)
      const jsonMatch = responseContent.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        projectAnalysis = JSON.parse(jsonMatch[0])
      } else {
        projectAnalysis = JSON.parse(responseContent)
      }
    } catch (parseError) {
      console.error('Error parsing AI analysis:', parseError)
      throw new Error('فشل في تحليل متطلبات المشروع')
    }

    // Step 2: Generate actual code for each file
    console.log(`📁 Generating ${projectAnalysis.files.length} files...`)
    const files = await generateRealProjectFiles(projectAnalysis, prompt)

    return {
      success: true,
      description: `تم إنشاء ${projectAnalysis.projectName} بنجاح باستخدام الذكاء الصناعي! المشروع يحتوي على ${files.length} ملفات فعلية.`,
      project: {
        id: Date.now().toString(),
        name: projectAnalysis.projectName,
        description: projectAnalysis.description,
        type: projectAnalysis.projectType,
        features: projectAnalysis.features,
        technologies: projectAnalysis.technologies,
        createdAt: new Date().toISOString()
      },
      files: files,
      fileTree: generateFileTree(files)
    }

  } catch (error) {
    console.error('❌ Error with OpenAI API:', error)

    return {
      success: false,
      error: `خطأ في الذكاء الصناعي: ${error.message}`
    }
  }
}

async function generateRealProjectFiles(analysis, originalPrompt) {
  const files = []

  for (const fileSpec of analysis.files) {
    try {
      console.log(`🔄 Generating ${fileSpec.name}...`)

      const filePrompt = `
أنشئ محتوى الملف التالي بشكل كامل وعملي:

السياق:
- المشروع: ${analysis.projectName}
- النوع: ${analysis.projectType}
- الوصف: ${analysis.description}
- الطلب الأصلي: "${originalPrompt}"

الملف المطلوب:
- الاسم: ${fileSpec.name}
- المسار: ${fileSpec.path}
- اللغة: ${fileSpec.language}
- الغرض: ${fileSpec.description}

المتطلبات:
1. كود كامل وجاهز للتشغيل
2. أفضل الممارسات البرمجية
3. تعليقات واضحة بالعربية
4. تصميم متجاوب (للواجهات)
5. معالجة الأخطاء المناسبة
6. استخدام التقنيات الحديثة

الميزات المطلوبة: ${analysis.features.join(', ')}
التقنيات: ${analysis.technologies.join(', ')}

أرجع محتوى الملف فقط بدون أي نص إضافي أو تفسيرات.
`

      const fileResponse = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: `أنت مطور خبير متخصص في ${fileSpec.language}. تكتب كود عالي الجودة، عملي، وجاهز للإنتاج. ترد بالكود فقط بدون أي تفسيرات.`
          },
          {
            role: "user",
            content: filePrompt
          }
        ],
        temperature: 0.1, // Low temperature for consistent, reliable code
        max_tokens: 3000
      })

      let fileContent = fileResponse.choices[0].message.content.trim()

      // Clean up the response (remove markdown code blocks if present)
      fileContent = fileContent.replace(/^```[\w]*\n/, '').replace(/\n```$/, '')

      files.push({
        id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        name: fileSpec.name,
        path: fileSpec.path,
        content: fileContent,
        language: fileSpec.language,
        description: fileSpec.description
      })

      // Add delay to respect API rate limits
      await new Promise(resolve => setTimeout(resolve, 1000))

    } catch (error) {
      console.error(`❌ Error generating file ${fileSpec.name}:`, error)

      // Create a placeholder file with error info
      files.push({
        id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        name: fileSpec.name,
        path: fileSpec.path,
        content: `// خطأ في توليد الملف: ${error.message}\n// يرجى المحاولة مرة أخرى\n\n// ${fileSpec.description}`,
        language: fileSpec.language,
        description: fileSpec.description
      })
    }
  }

  console.log(`✅ Generated ${files.length} files successfully`)
  return files
}

function getBasicTemplate(fileTemplate) {
  const templates = {
    'package.json': `{
  "name": "my-project",
  "version": "1.0.0",
  "description": "مشروع تم إنشاؤه بواسطة الذكاء الصناعي",
  "main": "index.js",
  "scripts": {
    "start": "node index.js",
    "dev": "nodemon index.js"
  },
  "dependencies": {},
  "devDependencies": {}
}`,
    'index.html': `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مشروع جديد</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <h1>مرحباً بك في مشروعك الجديد!</h1>
        <p>هذا مشروع تم إنشاؤه بواسطة الذكاء الصناعي.</p>
    </div>
    <script src="js/script.js"></script>
</body>
</html>`,
    'style.css': `/* ملف الأنماط الرئيسي */
body {
    font-family: 'Cairo', sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
    direction: rtl;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

h1 {
    color: #333;
    text-align: center;
}`,
    'script.js': `// ملف JavaScript الرئيسي
console.log('مرحباً من JavaScript!');

// إضافة تفاعل أساسي
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل الصفحة بنجاح');
});`
  }

  return templates[fileTemplate.name] || `// ${fileTemplate.name}\n// ملف تم إنشاؤه بواسطة الذكاء الصناعي\n`
}

// Mock project generation for development
function generateMockProject(prompt) {
  const input = prompt.toLowerCase()
  
  // Determine project type based on keywords
  let projectType = 'html'
  let projectName = 'مشروع جديد'
  let description = 'مشروع تم إنشاؤه بواسطة الذكاء الصناعي'
  
  if (input.includes('react') || input.includes('ريأكت')) {
    projectType = 'react'
    projectName = 'تطبيق React'
    description = 'تطبيق React حديث مع مكونات تفاعلية'
  } else if (input.includes('node') || input.includes('api') || input.includes('express')) {
    projectType = 'nodejs'
    projectName = 'API Node.js'
    description = 'واجهة برمجة تطبيقات RESTful باستخدام Node.js'
  } else if (input.includes('مهام') || input.includes('todo')) {
    projectType = 'html'
    projectName = 'مدير المهام'
    description = 'تطبيق لإدارة المهام اليومية'
  }

  const template = projectTemplates[projectType]
  const files = template.files.map(fileTemplate => ({
    id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    name: fileTemplate.name,
    path: fileTemplate.path,
    content: getBasicTemplate(fileTemplate),
    language: fileTemplate.language
  }))

  return {
    success: true,
    description: `تم إنشاء ${projectName} بنجاح! المشروع يحتوي على ${files.length} ملفات.`,
    project: {
      id: Date.now().toString(),
      name: projectName,
      description: description,
      type: projectType,
      createdAt: new Date().toISOString()
    },
    files: files,
    fileTree: generateFileTree(files)
  }
}

// Generate file tree structure
function generateFileTree(files) {
  const tree = []
  const folders = new Map()

  files.forEach(file => {
    const pathParts = file.path.split('/')
    let currentLevel = tree
    let currentPath = ''

    pathParts.forEach((part, index) => {
      currentPath = currentPath ? `${currentPath}/${part}` : part
      
      if (index === pathParts.length - 1) {
        // This is a file
        currentLevel.push({
          id: file.id,
          name: part,
          type: 'file',
          path: file.path,
          language: file.language
        })
      } else {
        // This is a folder
        let folder = currentLevel.find(item => item.name === part && item.type === 'folder')
        if (!folder) {
          folder = {
            id: `folder-${currentPath}`,
            name: part,
            type: 'folder',
            path: currentPath,
            children: []
          }
          currentLevel.push(folder)
        }
        currentLevel = folder.children
      }
    })
  })

  return tree
}
