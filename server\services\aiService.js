import OpenAI from 'openai'

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

// Project templates for different types
const projectTemplates = {
  react: {
    name: 'React Application',
    description: 'Modern React application with components',
    files: [
      {
        name: 'package.json',
        path: 'package.json',
        language: 'json',
        template: true
      },
      {
        name: 'index.html',
        path: 'public/index.html',
        language: 'html',
        template: true
      },
      {
        name: 'App.jsx',
        path: 'src/App.jsx',
        language: 'javascript',
        template: true
      }
    ]
  },
  html: {
    name: 'HTML Website',
    description: 'Static HTML website with CSS and JavaScript',
    files: [
      {
        name: 'index.html',
        path: 'index.html',
        language: 'html',
        template: true
      },
      {
        name: 'style.css',
        path: 'css/style.css',
        language: 'css',
        template: true
      },
      {
        name: 'script.js',
        path: 'js/script.js',
        language: 'javascript',
        template: true
      }
    ]
  },
  nodejs: {
    name: 'Node.js API',
    description: 'RESTful API built with Node.js and Express',
    files: [
      {
        name: 'package.json',
        path: 'package.json',
        language: 'json',
        template: true
      },
      {
        name: 'server.js',
        path: 'server.js',
        language: 'javascript',
        template: true
      },
      {
        name: 'routes.js',
        path: 'routes/index.js',
        language: 'javascript',
        template: true
      }
    ]
  }
}

export async function generateProjectWithAI(prompt) {
  try {
    // For development/demo purposes, use mock generation if no API key
    if (!process.env.OPENAI_API_KEY) {
      console.log('No OpenAI API key found, using mock generation')
      return generateMockProject(prompt)
    }

    // Analyze the prompt to determine project type and requirements
    const analysisPrompt = `
تحليل الطلب التالي وتحديد نوع المشروع والمتطلبات:

الطلب: "${prompt}"

يرجى الرد بتنسيق JSON يحتوي على:
{
  "projectType": "react|html|nodejs|vue|angular",
  "projectName": "اسم المشروع",
  "description": "وصف المشروع",
  "features": ["قائمة بالميزات المطلوبة"],
  "technologies": ["قائمة بالتقنيات المستخدمة"]
}
`

    const analysisResponse = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "أنت مطور خبير يحلل متطلبات المشاريع البرمجية ويحدد التقنيات المناسبة."
        },
        {
          role: "user",
          content: analysisPrompt
        }
      ],
      temperature: 0.3,
    })

    let projectAnalysis
    try {
      projectAnalysis = JSON.parse(analysisResponse.choices[0].message.content)
    } catch (parseError) {
      console.error('Error parsing AI analysis:', parseError)
      return generateMockProject(prompt)
    }

    // Generate project files based on analysis
    const files = await generateProjectFiles(projectAnalysis, prompt)

    return {
      success: true,
      description: `تم إنشاء ${projectAnalysis.projectName} بنجاح! المشروع يحتوي على ${files.length} ملفات.`,
      project: {
        id: Date.now().toString(),
        name: projectAnalysis.projectName,
        description: projectAnalysis.description,
        type: projectAnalysis.projectType,
        features: projectAnalysis.features,
        technologies: projectAnalysis.technologies,
        createdAt: new Date().toISOString()
      },
      files: files,
      fileTree: generateFileTree(files)
    }

  } catch (error) {
    console.error('Error with OpenAI API:', error)
    
    // Fallback to mock generation
    console.log('Falling back to mock generation')
    return generateMockProject(prompt)
  }
}

async function generateProjectFiles(analysis, originalPrompt) {
  const files = []
  const template = projectTemplates[analysis.projectType] || projectTemplates.html

  for (const fileTemplate of template.files) {
    try {
      const filePrompt = `
إنشاء محتوى الملف التالي:

نوع المشروع: ${analysis.projectType}
اسم المشروع: ${analysis.projectName}
وصف المشروع: ${analysis.description}
الطلب الأصلي: "${originalPrompt}"

الملف المطلوب:
- الاسم: ${fileTemplate.name}
- المسار: ${fileTemplate.path}
- اللغة: ${fileTemplate.language}

يرجى إنشاء محتوى الملف بشكل كامل وعملي، مع مراعاة:
1. أفضل الممارسات في البرمجة
2. التعليقات باللغة العربية
3. الكود يجب أن يكون جاهز للتشغيل
4. استخدام التقنيات الحديثة

أرجع فقط محتوى الملف بدون أي تفسيرات إضافية.
`

      const fileResponse = await openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          {
            role: "system",
            content: `أنت مطور خبير متخصص في ${fileTemplate.language}. تنشئ كود عالي الجودة وجاهز للإنتاج.`
          },
          {
            role: "user",
            content: filePrompt
          }
        ],
        temperature: 0.2,
        max_tokens: 2000
      })

      const fileContent = fileResponse.choices[0].message.content.trim()

      files.push({
        id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        name: fileTemplate.name,
        path: fileTemplate.path,
        content: fileContent,
        language: fileTemplate.language
      })

      // Add delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 500))

    } catch (error) {
      console.error(`Error generating file ${fileTemplate.name}:`, error)
      
      // Add a basic template file if generation fails
      files.push({
        id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        name: fileTemplate.name,
        path: fileTemplate.path,
        content: getBasicTemplate(fileTemplate),
        language: fileTemplate.language
      })
    }
  }

  return files
}

function getBasicTemplate(fileTemplate) {
  const templates = {
    'package.json': `{
  "name": "my-project",
  "version": "1.0.0",
  "description": "مشروع تم إنشاؤه بواسطة الذكاء الصناعي",
  "main": "index.js",
  "scripts": {
    "start": "node index.js",
    "dev": "nodemon index.js"
  },
  "dependencies": {},
  "devDependencies": {}
}`,
    'index.html': `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مشروع جديد</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <h1>مرحباً بك في مشروعك الجديد!</h1>
        <p>هذا مشروع تم إنشاؤه بواسطة الذكاء الصناعي.</p>
    </div>
    <script src="js/script.js"></script>
</body>
</html>`,
    'style.css': `/* ملف الأنماط الرئيسي */
body {
    font-family: 'Cairo', sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
    direction: rtl;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

h1 {
    color: #333;
    text-align: center;
}`,
    'script.js': `// ملف JavaScript الرئيسي
console.log('مرحباً من JavaScript!');

// إضافة تفاعل أساسي
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل الصفحة بنجاح');
});`
  }

  return templates[fileTemplate.name] || `// ${fileTemplate.name}\n// ملف تم إنشاؤه بواسطة الذكاء الصناعي\n`
}

// Mock project generation for development
function generateMockProject(prompt) {
  const input = prompt.toLowerCase()
  
  // Determine project type based on keywords
  let projectType = 'html'
  let projectName = 'مشروع جديد'
  let description = 'مشروع تم إنشاؤه بواسطة الذكاء الصناعي'
  
  if (input.includes('react') || input.includes('ريأكت')) {
    projectType = 'react'
    projectName = 'تطبيق React'
    description = 'تطبيق React حديث مع مكونات تفاعلية'
  } else if (input.includes('node') || input.includes('api') || input.includes('express')) {
    projectType = 'nodejs'
    projectName = 'API Node.js'
    description = 'واجهة برمجة تطبيقات RESTful باستخدام Node.js'
  } else if (input.includes('مهام') || input.includes('todo')) {
    projectType = 'html'
    projectName = 'مدير المهام'
    description = 'تطبيق لإدارة المهام اليومية'
  }

  const template = projectTemplates[projectType]
  const files = template.files.map(fileTemplate => ({
    id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    name: fileTemplate.name,
    path: fileTemplate.path,
    content: getBasicTemplate(fileTemplate),
    language: fileTemplate.language
  }))

  return {
    success: true,
    description: `تم إنشاء ${projectName} بنجاح! المشروع يحتوي على ${files.length} ملفات.`,
    project: {
      id: Date.now().toString(),
      name: projectName,
      description: description,
      type: projectType,
      createdAt: new Date().toISOString()
    },
    files: files,
    fileTree: generateFileTree(files)
  }
}

// Generate file tree structure
function generateFileTree(files) {
  const tree = []
  const folders = new Map()

  files.forEach(file => {
    const pathParts = file.path.split('/')
    let currentLevel = tree
    let currentPath = ''

    pathParts.forEach((part, index) => {
      currentPath = currentPath ? `${currentPath}/${part}` : part
      
      if (index === pathParts.length - 1) {
        // This is a file
        currentLevel.push({
          id: file.id,
          name: part,
          type: 'file',
          path: file.path,
          language: file.language
        })
      } else {
        // This is a folder
        let folder = currentLevel.find(item => item.name === part && item.type === 'folder')
        if (!folder) {
          folder = {
            id: `folder-${currentPath}`,
            name: part,
            type: 'folder',
            path: currentPath,
            children: []
          }
          currentLevel.push(folder)
        }
        currentLevel = folder.children
      }
    })
  })

  return tree
}
