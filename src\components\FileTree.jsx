import React, { useState } from 'react'
import { 
  FiFile, 
  FiFolder, 
  FiFolderOpen, 
  FiPlus, 
  FiTrash2, 
  FiEdit3,
  FiMoreVertical
} from 'react-icons/fi'
import { useProject } from '../context/ProjectContext'

function FileTree({ onFileSelect, activeFile }) {
  const { fileTree, files, addFile, deleteFile, updateFile } = useProject()
  const [expandedFolders, setExpandedFolders] = useState(new Set())
  const [contextMenu, setContextMenu] = useState(null)
  const [editingItem, setEditingItem] = useState(null)
  const [newItemName, setNewItemName] = useState('')

  const toggleFolder = (folderId) => {
    const newExpanded = new Set(expandedFolders)
    if (newExpanded.has(folderId)) {
      newExpanded.delete(folderId)
    } else {
      newExpanded.add(folderId)
    }
    setExpandedFolders(newExpanded)
  }

  const handleFileClick = (fileId) => {
    const file = files[fileId]
    if (file) {
      onFileSelect(file)
    }
  }

  const handleContextMenu = (e, item) => {
    e.preventDefault()
    setContextMenu({
      x: e.clientX,
      y: e.clientY,
      item
    })
  }

  const closeContextMenu = () => {
    setContextMenu(null)
  }

  const handleAddFile = (parentPath = '') => {
    const fileName = prompt('اسم الملف الجديد:')
    if (fileName) {
      const filePath = parentPath ? `${parentPath}/${fileName}` : fileName
      const newFile = {
        name: fileName,
        path: filePath,
        content: '',
        language: getLanguageFromExtension(fileName)
      }
      addFile(newFile)
    }
    closeContextMenu()
  }

  const handleAddFolder = (parentPath = '') => {
    const folderName = prompt('اسم المجلد الجديد:')
    if (folderName) {
      // Add a placeholder file to create the folder structure
      const filePath = parentPath ? `${parentPath}/${folderName}/.gitkeep` : `${folderName}/.gitkeep`
      const newFile = {
        name: '.gitkeep',
        path: filePath,
        content: '',
        language: 'text'
      }
      addFile(newFile)
    }
    closeContextMenu()
  }

  const handleRename = (item) => {
    setEditingItem(item.id)
    setNewItemName(item.name)
    closeContextMenu()
  }

  const handleDelete = (item) => {
    if (window.confirm(`هل أنت متأكد من حذف ${item.name}؟`)) {
      if (item.type === 'file') {
        deleteFile(item.id)
      }
      // TODO: Handle folder deletion
    }
    closeContextMenu()
  }

  const handleRenameSubmit = (item) => {
    if (newItemName.trim() && newItemName !== item.name) {
      const newPath = item.path.replace(item.name, newItemName.trim())
      updateFile(item.id, {
        name: newItemName.trim(),
        path: newPath
      })
    }
    setEditingItem(null)
    setNewItemName('')
  }

  const getLanguageFromExtension = (fileName) => {
    const ext = fileName.split('.').pop().toLowerCase()
    const languageMap = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'json': 'json',
      'md': 'markdown',
      'py': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'php': 'php',
      'rb': 'ruby',
      'go': 'go',
      'rs': 'rust',
      'vue': 'vue',
      'xml': 'xml',
      'yaml': 'yaml',
      'yml': 'yaml'
    }
    return languageMap[ext] || 'text'
  }

  const getFileIcon = (fileName, language) => {
    const ext = fileName.split('.').pop().toLowerCase()
    const iconClass = "w-4 h-4"
    
    // You can customize icons based on file type
    return <FiFile className={iconClass} />
  }

  const renderTreeItem = (item, level = 0) => {
    const isExpanded = expandedFolders.has(item.id)
    const isActive = activeFile?.id === item.id
    const isEditing = editingItem === item.id

    return (
      <div key={item.id}>
        <div
          className={`file-tree-item flex items-center gap-2 px-2 py-1 cursor-pointer hover:bg-gray-50 ${
            isActive ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
          }`}
          style={{ paddingRight: `${(level * 20) + 8}px` }}
          onClick={() => {
            if (item.type === 'folder') {
              toggleFolder(item.id)
            } else {
              handleFileClick(item.id)
            }
          }}
          onContextMenu={(e) => handleContextMenu(e, item)}
        >
          {/* Icon */}
          <div className="flex-shrink-0">
            {item.type === 'folder' ? (
              isExpanded ? (
                <FiFolderOpen className="w-4 h-4 text-blue-500" />
              ) : (
                <FiFolder className="w-4 h-4 text-blue-500" />
              )
            ) : (
              getFileIcon(item.name, item.language)
            )}
          </div>

          {/* Name */}
          <div className="flex-1 min-w-0">
            {isEditing ? (
              <input
                type="text"
                value={newItemName}
                onChange={(e) => setNewItemName(e.target.value)}
                onBlur={() => handleRenameSubmit(item)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleRenameSubmit(item)
                  } else if (e.key === 'Escape') {
                    setEditingItem(null)
                    setNewItemName('')
                  }
                }}
                className="w-full px-1 py-0 text-sm border border-blue-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                autoFocus
              />
            ) : (
              <span className="text-sm truncate">{item.name}</span>
            )}
          </div>
        </div>

        {/* Children */}
        {item.type === 'folder' && isExpanded && item.children && (
          <div>
            {item.children.map(child => renderTreeItem(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  // Click outside to close context menu
  React.useEffect(() => {
    const handleClick = () => closeContextMenu()
    if (contextMenu) {
      document.addEventListener('click', handleClick)
      return () => document.removeEventListener('click', handleClick)
    }
  }, [contextMenu])

  if (!fileTree || fileTree.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500">
        <FiFolder className="w-8 h-8 mx-auto mb-2 text-gray-300" />
        <p className="text-sm">لا توجد ملفات</p>
        <p className="text-xs mt-1">ابدأ محادثة لإنشاء مشروع</p>
      </div>
    )
  }

  return (
    <div className="h-full bg-white">
      {/* Header */}
      <div className="p-3 border-b border-gray-200 flex items-center justify-between">
        <h3 className="text-sm font-medium text-gray-800">ملفات المشروع</h3>
        <button
          onClick={() => handleAddFile()}
          className="p-1 hover:bg-gray-100 rounded text-gray-500 hover:text-gray-700"
          title="إضافة ملف جديد"
        >
          <FiPlus className="w-4 h-4" />
        </button>
      </div>

      {/* Tree */}
      <div className="overflow-auto">
        {fileTree.map(item => renderTreeItem(item))}
      </div>

      {/* Context Menu */}
      {contextMenu && (
        <div
          className="fixed bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-50"
          style={{
            left: contextMenu.x,
            top: contextMenu.y
          }}
        >
          <button
            onClick={() => handleAddFile(contextMenu.item.path)}
            className="w-full px-3 py-2 text-right text-sm hover:bg-gray-50 flex items-center gap-2"
          >
            <FiFile className="w-4 h-4" />
            إضافة ملف
          </button>
          <button
            onClick={() => handleAddFolder(contextMenu.item.path)}
            className="w-full px-3 py-2 text-right text-sm hover:bg-gray-50 flex items-center gap-2"
          >
            <FiFolder className="w-4 h-4" />
            إضافة مجلد
          </button>
          <hr className="my-1" />
          <button
            onClick={() => handleRename(contextMenu.item)}
            className="w-full px-3 py-2 text-right text-sm hover:bg-gray-50 flex items-center gap-2"
          >
            <FiEdit3 className="w-4 h-4" />
            إعادة تسمية
          </button>
          <button
            onClick={() => handleDelete(contextMenu.item)}
            className="w-full px-3 py-2 text-right text-sm hover:bg-gray-50 text-red-600 flex items-center gap-2"
          >
            <FiTrash2 className="w-4 h-4" />
            حذف
          </button>
        </div>
      )}
    </div>
  )
}

export default FileTree
