#!/usr/bin/env node

/**
 * Quick start script for AI Project Builder
 * This script helps users get started quickly by:
 * 1. Installing dependencies
 * 2. Setting up environment variables
 * 3. Starting both frontend and backend
 */

import { spawn } from 'child_process'
import { existsSync, writeFileSync } from 'fs'
import { join } from 'path'
import { fileURLToPath } from 'url'
import { dirname } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

console.log('🚀 مرحباً بك في مولد المشاريع الذكي!')
console.log('====================================')

// Check if node_modules exists
if (!existsSync(join(__dirname, 'node_modules'))) {
  console.log('📦 تثبيت المتطلبات...')
  
  const install = spawn('npm', ['install'], {
    stdio: 'inherit',
    shell: true,
    cwd: __dirname
  })

  install.on('close', (code) => {
    if (code === 0) {
      console.log('✅ تم تثبيت المتطلبات بنجاح!')
      setupEnvironment()
    } else {
      console.error('❌ فشل في تثبيت المتطلبات')
      process.exit(1)
    }
  })
} else {
  setupEnvironment()
}

function setupEnvironment() {
  // Check if .env exists
  if (!existsSync(join(__dirname, '.env'))) {
    console.log('⚙️ إعداد متغيرات البيئة...')
    
    const envContent = `# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Server Configuration
PORT=5000
NODE_ENV=development
`
    
    writeFileSync(join(__dirname, '.env'), envContent)
    console.log('📝 تم إنشاء ملف .env')
    console.log('💡 يرجى إضافة مفتاح OpenAI API في ملف .env للحصول على أفضل تجربة')
  }

  startServers()
}

function startServers() {
  console.log('🔥 بدء تشغيل الخوادم...')
  
  // Start backend server
  console.log('🖥️ تشغيل الخادم الخلفي على المنفذ 5000...')
  const backend = spawn('node', ['server/index.js'], {
    stdio: 'pipe',
    shell: true,
    cwd: __dirname,
    env: { ...process.env, NODE_ENV: 'development' }
  })

  backend.stdout.on('data', (data) => {
    console.log(`[Backend] ${data.toString().trim()}`)
  })

  backend.stderr.on('data', (data) => {
    console.error(`[Backend Error] ${data.toString().trim()}`)
  })

  // Wait a bit for backend to start, then start frontend
  setTimeout(() => {
    console.log('🎨 تشغيل الواجهة الأمامية على المنفذ 3000...')
    const frontend = spawn('npm', ['run', 'dev'], {
      stdio: 'pipe',
      shell: true,
      cwd: __dirname
    })

    frontend.stdout.on('data', (data) => {
      const output = data.toString().trim()
      if (output.includes('Local:')) {
        console.log('✅ التطبيق جاهز!')
        console.log('🌐 افتح المتصفح وانتقل إلى: http://localhost:3000')
        console.log('📚 للمساعدة، راجع ملف README.md')
      }
      console.log(`[Frontend] ${output}`)
    })

    frontend.stderr.on('data', (data) => {
      console.error(`[Frontend Error] ${data.toString().trim()}`)
    })

    // Handle process termination
    process.on('SIGINT', () => {
      console.log('\n🛑 إيقاف الخوادم...')
      backend.kill()
      frontend.kill()
      process.exit(0)
    })

  }, 3000)
}

// Handle errors
process.on('uncaughtException', (error) => {
  console.error('❌ خطأ غير متوقع:', error.message)
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ رفض غير معالج:', reason)
  process.exit(1)
})
