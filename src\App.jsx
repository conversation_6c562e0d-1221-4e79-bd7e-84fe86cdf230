import React, { useState, useCallback } from 'react'
import ChatPanel from './components/ChatPanel'
import FileTree from './components/FileTree'
import CodeEditor from './components/CodeEditor'
import Preview from './components/Preview'
import Header from './components/Header'
import { ProjectProvider } from './context/ProjectContext'

function App() {
  const [activeFile, setActiveFile] = useState(null)
  const [showPreview, setShowPreview] = useState(false)
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)

  const handleFileSelect = useCallback((file) => {
    setActiveFile(file)
  }, [])

  const togglePreview = useCallback(() => {
    setShowPreview(prev => !prev)
  }, [])

  const toggleSidebar = useCallback(() => {
    setSidebarCollapsed(prev => !prev)
  }, [])

  return (
    <ProjectProvider>
      <div className="h-screen flex flex-col bg-gray-50">
        {/* Header */}
        <Header 
          onTogglePreview={togglePreview}
          onToggleSidebar={toggleSidebar}
          showPreview={showPreview}
          sidebarCollapsed={sidebarCollapsed}
        />

        {/* Main Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Sidebar */}
          <div className={`bg-white border-l border-gray-200 transition-all duration-300 ${
            sidebarCollapsed ? 'w-0' : 'w-80'
          } overflow-hidden`}>
            <div className="h-full flex flex-col">
              {/* Chat Panel */}
              <div className="flex-1 border-b border-gray-200">
                <ChatPanel />
              </div>
              
              {/* File Tree */}
              <div className="h-80 overflow-auto">
                <FileTree onFileSelect={handleFileSelect} activeFile={activeFile} />
              </div>
            </div>
          </div>

          {/* Main Editor Area */}
          <div className="flex-1 flex">
            {/* Code Editor */}
            <div className={`${showPreview ? 'w-1/2' : 'w-full'} transition-all duration-300`}>
              <CodeEditor activeFile={activeFile} />
            </div>

            {/* Preview Panel */}
            {showPreview && (
              <div className="w-1/2 border-l border-gray-200">
                <Preview />
              </div>
            )}
          </div>
        </div>
      </div>
    </ProjectProvider>
  )
}

export default App
