import React, { useRef, useEffect, useState } from 'react'
import { 
  FiRefreshCw, 
  FiExternalLink, 
  FiMonitor, 
  FiSmartphone, 
  FiTablet,
  FiAlertCircle
} from 'react-icons/fi'
import { useProject } from '../context/ProjectContext'

function Preview() {
  const iframeRef = useRef(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)
  const [viewMode, setViewMode] = useState('desktop') // desktop, tablet, mobile
  const { files, fileTree, project } = useProject()

  const generatePreviewHTML = () => {
    try {
      // Find HTML files
      const htmlFiles = Object.values(files).filter(file => 
        file.language === 'html' || file.name.endsWith('.html')
      )

      // Find CSS files
      const cssFiles = Object.values(files).filter(file => 
        file.language === 'css' || file.name.endsWith('.css')
      )

      // Find JavaScript files
      const jsFiles = Object.values(files).filter(file => 
        (file.language === 'javascript' || file.name.endsWith('.js') || file.name.endsWith('.jsx')) &&
        !file.name.includes('node_modules')
      )

      let htmlContent = ''

      if (htmlFiles.length > 0) {
        // Use the first HTML file as base
        const mainHtml = htmlFiles.find(f => f.name === 'index.html') || htmlFiles[0]
        htmlContent = mainHtml.content
      } else {
        // Generate basic HTML structure
        htmlContent = `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${project?.name || 'معاينة المشروع'}</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>معاينة المشروع</h1>
        <div id="root"></div>
    </div>
</body>
</html>`
      }

      // Inject CSS files
      if (cssFiles.length > 0) {
        const cssContent = cssFiles.map(file => file.content).join('\n')
        const styleTag = `<style>\n${cssContent}\n</style>`
        
        if (htmlContent.includes('</head>')) {
          htmlContent = htmlContent.replace('</head>', `${styleTag}\n</head>`)
        } else {
          htmlContent = `<style>${cssContent}</style>\n${htmlContent}`
        }
      }

      // Inject JavaScript files
      if (jsFiles.length > 0) {
        const jsContent = jsFiles.map(file => {
          // Simple JSX to JS transformation for basic cases
          let content = file.content
          
          // Handle React JSX (very basic transformation)
          if (file.name.endsWith('.jsx') || content.includes('React')) {
            content = transformJSX(content)
          }
          
          return content
        }).join('\n')

        const scriptTag = `<script>\n${jsContent}\n</script>`
        
        if (htmlContent.includes('</body>')) {
          htmlContent = htmlContent.replace('</body>', `${scriptTag}\n</body>`)
        } else {
          htmlContent = `${htmlContent}\n<script>${jsContent}</script>`
        }
      }

      return htmlContent
    } catch (err) {
      console.error('Error generating preview:', err)
      setError('خطأ في إنشاء المعاينة: ' + err.message)
      return `<html><body><div style="padding: 20px; color: red;">خطأ في إنشاء المعاينة: ${err.message}</div></body></html>`
    }
  }

  const transformJSX = (jsxCode) => {
    // Very basic JSX transformation - this is simplified
    // In a real implementation, you'd use Babel or similar
    try {
      let transformed = jsxCode
      
      // Replace JSX elements with basic HTML (very simplified)
      transformed = transformed.replace(/className=/g, 'class=')
      transformed = transformed.replace(/onClick=/g, 'onclick=')
      
      // Handle React.createElement calls (simplified)
      transformed = transformed.replace(/React\.createElement/g, 'document.createElement')
      
      return transformed
    } catch (err) {
      return jsxCode // Return original if transformation fails
    }
  }

  const refreshPreview = () => {
    setIsLoading(true)
    setError(null)
    
    try {
      const htmlContent = generatePreviewHTML()
      const iframe = iframeRef.current
      
      if (iframe) {
        const doc = iframe.contentDocument || iframe.contentWindow.document
        doc.open()
        doc.write(htmlContent)
        doc.close()
      }
    } catch (err) {
      setError('خطأ في تحديث المعاينة: ' + err.message)
    } finally {
      setIsLoading(false)
    }
  }

  const openInNewTab = () => {
    const htmlContent = generatePreviewHTML()
    const blob = new Blob([htmlContent], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    window.open(url, '_blank')
  }

  const getViewModeStyles = () => {
    switch (viewMode) {
      case 'mobile':
        return { width: '375px', height: '667px' }
      case 'tablet':
        return { width: '768px', height: '1024px' }
      default:
        return { width: '100%', height: '100%' }
    }
  }

  useEffect(() => {
    if (Object.keys(files).length > 0) {
      refreshPreview()
    }
  }, [files])

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center gap-2">
          <FiMonitor className="w-4 h-4 text-gray-500" />
          <span className="text-sm font-medium text-gray-800">معاينة مباشرة</span>
        </div>

        <div className="flex items-center gap-1">
          {/* View Mode Buttons */}
          <div className="flex items-center gap-1 mr-2">
            <button
              onClick={() => setViewMode('desktop')}
              className={`p-2 rounded transition-colors ${
                viewMode === 'desktop' 
                  ? 'bg-blue-100 text-blue-600' 
                  : 'hover:bg-gray-200 text-gray-600'
              }`}
              title="عرض سطح المكتب"
            >
              <FiMonitor className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('tablet')}
              className={`p-2 rounded transition-colors ${
                viewMode === 'tablet' 
                  ? 'bg-blue-100 text-blue-600' 
                  : 'hover:bg-gray-200 text-gray-600'
              }`}
              title="عرض الجهاز اللوحي"
            >
              <FiTablet className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('mobile')}
              className={`p-2 rounded transition-colors ${
                viewMode === 'mobile' 
                  ? 'bg-blue-100 text-blue-600' 
                  : 'hover:bg-gray-200 text-gray-600'
              }`}
              title="عرض الهاتف المحمول"
            >
              <FiSmartphone className="w-4 h-4" />
            </button>
          </div>

          {/* Refresh Button */}
          <button
            onClick={refreshPreview}
            disabled={isLoading}
            className="p-2 hover:bg-gray-200 rounded text-gray-600 hover:text-gray-800 transition-colors"
            title="تحديث المعاينة"
          >
            <FiRefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          </button>

          {/* Open in New Tab */}
          <button
            onClick={openInNewTab}
            className="p-2 hover:bg-gray-200 rounded text-gray-600 hover:text-gray-800 transition-colors"
            title="فتح في تبويب جديد"
          >
            <FiExternalLink className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Preview Content */}
      <div className="flex-1 overflow-auto bg-gray-100 p-4">
        {error ? (
          <div className="h-full flex items-center justify-center">
            <div className="text-center text-red-600">
              <FiAlertCircle className="w-12 h-12 mx-auto mb-4" />
              <p className="text-lg font-medium mb-2">خطأ في المعاينة</p>
              <p className="text-sm">{error}</p>
              <button
                onClick={refreshPreview}
                className="mt-4 px-4 py-2 bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors"
              >
                إعادة المحاولة
              </button>
            </div>
          </div>
        ) : Object.keys(files).length === 0 ? (
          <div className="h-full flex items-center justify-center">
            <div className="text-center text-gray-500">
              <FiMonitor className="w-16 h-16 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium mb-2">لا توجد ملفات للمعاينة</h3>
              <p className="text-sm">أنشئ مشروعاً أولاً لرؤية المعاينة</p>
            </div>
          </div>
        ) : (
          <div className="h-full flex items-center justify-center">
            <div 
              className="bg-white rounded-lg shadow-lg overflow-hidden transition-all duration-300"
              style={getViewModeStyles()}
            >
              {isLoading && (
                <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
                  <div className="flex items-center gap-2 text-gray-600">
                    <div className="spinner"></div>
                    <span>جاري التحميل...</span>
                  </div>
                </div>
              )}
              <iframe
                ref={iframeRef}
                className="w-full h-full border-0"
                title="معاينة المشروع"
                sandbox="allow-scripts allow-same-origin"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default Preview
